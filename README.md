# Faplytics Monitoring with Plugin Architecture

Provides a framework to run tests on the faplytics dataset in a highly constumizable and extendable way.

## Idea

The architecture follows a plugin/factory pattern. Each test type is a seperate plugin, which can be indivdually configured to run with different parameters and on different datasets.
Tests get automatically deployed as chron jobs on the respective databricks workspace and run every 4hrs using [databricks-jockey](https://git.daimler.com/ca4ad/databricks-jockey)
Each test result produces an output in the monitoring dataframe that follows this the schema of [ResultStatus](./faplytics_monitoring/core/dataset_utils.py).

## How to write your own datatest.

1. Each test should follow the [template](./faplytics_monitoring/plugins/template.py) and should inherit from the abstract base class [FaplyticsDataTest](./faplytics_monitoring/core/datatest.py).
2. After the `run()` method of the your test was executed, the values specified in the `__post_init_method__` of the [template](./faplytics_monitoring/plugins/template.py) should be set, as they consitute the output of the test which is written into the monitoring dataframe. Be Aware that for the status code Critical (4) a notification is sent to our teams channel.
3. The `dataset` parameter gets passed on from the config file, while addional parameters must be supplied as key-value pairs under `parameters` in the [config file](./faplytics_monitoring/config_prod.yaml).
4. If you want to run the test for several dataframes, the test must be registered individually for each dataframe in the config file. Attention: the test should also be mentioned in the `plugins` section of the config file.
5. Open a PR with your test and add it to the `config_testing_int/prod.yaml` the tests specified there are deployed in the PR-triggered devops pipeline and start immedialty on databricks. Once the job finishes, you can check the produced result (df.show() on the result dataframe) in the output of the `databricks-monitoring-testjob` in databricks.
6. Once you are happy with your test, integrate the configuration into the `config_int/prod.yaml` and merge the PR.


The plugin architecture was inspired by [ArjanCodes](https://github.com/ArjanCodes/2021-plugin-architecture/tree/main/after)
