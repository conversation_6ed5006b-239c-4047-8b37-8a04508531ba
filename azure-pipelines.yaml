trigger:
  tags:
    include:
      - "*"
  branches:
    include:
      - main
pr:
  - main

pool: ca4ad-common-build

resources:
  repositories:
    - repository: templates
      type: githubenterprise
      endpoint: DaimlerGitHub
      name: ca4ad/az-devops-shared
      ref: refs/tags/v1.0.0

variables:
  - name: djVersion
    value: 0.5.1
  - name: pythonVersion
    value: 3.9

stages:
- stage: Build
  jobs:
    - job: Build
      steps:
      - checkout: self
        persistCredentials: true
        fetchDepth: 0

      - template: templates/build-python.yml@templates  # Template reference
        parameters:
          package: faplytics_monitoring
          versionCommand: python -m setuptools_scm
          pipCacheKey: pip | pyproject.toml

      - template: templates/make-release.yml@templates  # Template reference

      - task: PythonScript@0
        inputs:
          scriptSource: "filepath"
          scriptPath: "faplytics_monitoring/generate_jobs_yaml.py"

      - publish: 'jobs.yaml'
        displayName: 'Publish jobs.yaml scripts for later use in the deploy stage'
        artifact: jobsConfig

- stage: Deploy
  variables:
    - name: project_version
      value: $[stageDependencies.Build.Build.outputs['PyBuild.Version']]
  dependsOn: Build

  jobs:
    - job: Deploy_testing_jobs
      condition: |
        and(
          not(failed()),
          not(canceled()),
          eq(variables['Build.Reason'], 'PullRequest')
        )
      steps:
        - download: current
          artifact: jobsConfig

        - task: PipAuthenticate@1
          displayName: 'Authenticate to rdafc-pypi-virtual repository'
          inputs:
            pythonDownloadServiceConnections: rdafc-pypi-virtual
            onlyAddExtraIndex: true

        - task: MavenAuthenticate@0
          displayName: 'Maven artifactory auth'
          inputs:
            mavenServiceConnections: 'Artifactory Maven Repository'

        - template: templates/databricks-jockey-deploy.yml@templates
          parameters:
            deployVersion: $(djVersion)
            env: int_testing
            jobsFile: $(Pipeline.Workspace)/jobsConfig/jobs.yaml

        - template: templates/databricks-jockey-deploy.yml@templates
          parameters:
            deployVersion: $(djVersion)
            env: prod_testing
            jobsFile: $(Pipeline.Workspace)/jobsConfig/jobs.yaml


    - job: Deploy_production_jobs
      condition: |
        and(
          not(failed()),
          not(canceled()),
          or(
            startsWith(variables['Build.SourceBranch'], 'refs/heads/main'),
            startsWith(variables['Build.SourceBranch'], 'refs/tags/')
          )
        )
      steps:
        - download: current
          artifact: jobsConfig

        - task: PipAuthenticate@1
          displayName: 'Authenticate to rdafc-pypi-virtual repository'
          inputs:
            pythonDownloadServiceConnections: rdafc-pypi-virtual
            onlyAddExtraIndex: true

        - task: MavenAuthenticate@0
          displayName: 'Maven artifactory auth'
          inputs:
            mavenServiceConnections: 'Artifactory Maven Repository'

        - template: templates/databricks-jockey-deploy.yml@templates
          parameters:
            deployVersion: $(djVersion)
            env: int
            jobsFile: $(Pipeline.Workspace)/jobsConfig/jobs.yaml

        - template: templates/databricks-jockey-deploy.yml@templates
          parameters:
            deployVersion: $(djVersion)
            env: prod
            jobsFile: $(Pipeline.Workspace)/jobsConfig/jobs.yaml
