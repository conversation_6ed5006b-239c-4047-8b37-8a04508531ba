{"commitInfo":{"timestamp":1630482740609,"operation":"WRITE","operationParameters":{"mode":"ErrorIfExists","partitionBy":"[]"},"isBlindAppend":true,"operationMetrics":{"numFiles":"1","numOutputBytes":"34133","numOutputRows":"140"}}}
{"protocol":{"minReaderVersion":1,"minWriterVersion":2}}
{"metaData":{"id":"de8618ea-a22e-45a4-8582-30fd48c38f31","format":{"provider":"parquet","options":{}},"schemaString":"{\"type\":\"struct\",\"fields\":[{\"name\":\"event_type\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"req_id\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"baumuster\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"baureihe\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"position\",\"type\":{\"type\":\"udt\",\"class\":\"org.apache.spark.sql.sedona_sql.UDT.GeometryUDT\",\"pyClass\":\"sedona.sql.types.GeometryType\",\"sqlType\":{\"type\":\"array\",\"elementType\":\"byte\",\"containsNull\":false}},\"nullable\":true,\"metadata\":{}},{\"name\":\"heading\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"odometer\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"speed\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"trigger\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"source\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"event_slot\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"veda\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"veda_decode\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"hermes_scriptsnr\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"hermes_swsnr\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"hermes_savetime\",\"type\":\"timestamp\",\"nullable\":true,\"metadata\":{}},{\"name\":\"hermes_transtime\",\"type\":\"timestamp\",\"nullable\":true,\"metadata\":{}},{\"name\":\"idc_diag\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"idc_swk\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"idc_fwk\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"idc_hwk\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"idc_hwsnr\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"idc_fwsnr\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"data\",\"type\":{\"type\":\"map\",\"keyType\":\"string\",\"valueType\":\"long\",\"valueContainsNull\":true},\"nullable\":true,\"metadata\":{}},{\"name\":\"data_raw\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"vdc_date\",\"type\":\"timestamp\",\"nullable\":true,\"metadata\":{}},{\"name\":\"backend_process_date\",\"type\":\"timestamp\",\"nullable\":true,\"metadata\":{}},{\"name\":\"backend_received_date\",\"type\":\"timestamp\",\"nullable\":true,\"metadata\":{}},{\"name\":\"snapshot_id\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"event_id\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"vin\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"geohash\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"link_id\",\"type\":\"integer\",\"nullable\":true,\"metadata\":{}},{\"name\":\"dir_travel\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"distance\",\"type\":\"double\",\"nullable\":true,\"metadata\":{}},{\"name\":\"rel_dist_from_ref_node\",\"type\":\"double\",\"nullable\":true,\"metadata\":{}}]}","partitionColumns":[],"configuration":{},"createdTime":1630482739478}}
{"add":{"path":"part-00000-fa53cfa6-e344-49eb-95b8-0051e36dd05d-c000.snappy.parquet","partitionValues":{},"size":34133,"modificationTime":1630482740000,"dataChange":true}}
