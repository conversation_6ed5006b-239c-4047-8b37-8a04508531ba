import enum
import random
from datetime import datetime, timedelta

import pyspark.sql
import pytest

from faplytics_monitoring.core.dataset_utils import (
    ResultModel,
    write_to_monitoring_table,
)
from faplytics_monitoring.core.notification import NotificationRecord, Notifier


@pytest.fixture
def notifier(spark, template_testing_config, sample_test_result_saved) -> Notifier:
    return Notifier(spark=spark, config=template_testing_config)


def test_notification(notifier, send_mock):

    notifier.send_notifications()
    send_mock.assert_called_once()


def test_notification_no_fatal_error(notifier, send_mock, mocker):
    class AnEnum(enum.Enum):
        more_than_critical = 4

    notifier.send_notifications(level=AnEnum.more_than_critical)
    assert not send_mock.called


def test_notification_state_read(notifier):

    start = datetime(2023, 1, 1, 12, 0, 0)
    notifications = [
        NotificationRecord(
            time=start + timedelta(hours=hours),
            test_time=start + timedelta(hours=hours),
            test_id="some_test",
            state="state",
        )
        for hours in range(1, 1000)
    ]
    random.shuffle(notifications)
    latest = max(n.time for n in notifications)

    notifier.write_notified_state(notifications)
    state = notifier.read_notified_state()
    assert state[("some_test", "state")].time == latest


def test_notification_silence(notifier):

    result = notifier.send_notifications()
    assert len(result.recovered) == 0
    assert len(result.failed) == 2

    # write same results again (i.e. another tests-run produced the same result)
    _write_results([("dataset_3", 3), ("dataset_0", 0)], notifier.config)

    # expect no notifications since we're inside the silence-duration
    r2 = notifier.send_notifications()
    assert len(r2.failed) == 0
    assert len(r2.recovered) == 0

    # let the dataset_3 test recover and dataset_0 fail:
    _write_results([("dataset_3", 0), ("dataset_0", 3)], notifier.config)

    r3 = notifier.send_notifications()
    assert len(r3.failed) == 1
    assert r3.failed[0].dataset == "dataset_0"

    assert len(r3.recovered) == 1
    assert r3.recovered[0].dataset == "dataset_3"


def test_notification_digest(notifier):

    result = notifier.send_digest()
    assert len(result.recovered) == 0
    assert len(result.failed) == 1

    result = notifier.send_digest()
    assert len(result.failed) == 0
    assert len(result.recovered) == 0


def _write_results(results, config):
    data = [
        ResultModel(
            dataset=ds,
            test_type="data_count",
            test_name="data_count",
            test_group="basic",
            test_result_code=code,
            test_result_map={},
            html="",
            time=datetime.utcnow(),
        )
        for ds, code in results
    ]
    defs = config["definitions"]
    write_to_monitoring_table(
        data, defs["sql_table_name"], defs["sql_dataset_path"], pyspark.sql.SparkSession.getActiveSession()  # type: ignore
    )
