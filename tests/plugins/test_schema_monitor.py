from datetime import datetime

import pyspark.sql.functions as F
import pytest

from faplytics_monitoring.core.dataset_utils import (
    ResultModel,
    write_to_monitoring_table,
)
from faplytics_monitoring.core.errors import ResultStatus
from faplytics_monitoring.plugins.schema_monitor import SchemaMonitor


@pytest.fixture
def create_test_data(tmp_path, spark):
    data_ = [
        ("Unknown000000", "2021-04-15T09:48:00.000+0000", False),
        ("1HGCM82633A000000", "2021-04-15T10:48:00.000+0000", True),
    ]

    df = spark.createDataFrame(data_, schema="vin STRING, incident_time STRING, test_vehicle BOOLEAN")
    df = df.withColumn("incident_time", F.to_timestamp(F.col("incident_time")))

    df_path = str(tmp_path / "incident_sample_data")
    df.write.format("delta").mode("overwrite").save(df_path)

    return df_path, df.schema.json()


@pytest.fixture
def create_result_table():
    return ResultModel(
        time=datetime.utcnow(),
        dataset="fap5_incidents",
        test_type="schema_monitor",
        test_name="schema_monitor",
        test_group="basic",
        test_result_code=0,
        test_result_map={"schema": "{}"},
        html="",
    )


def test_schema_monitor_match(create_test_data, create_result_table, tmp_path, spark):
    previous_test_result_table = create_result_table
    previous_test_result_table.test_result_map["schema"] = create_test_data[1]

    write_to_monitoring_table(
        [previous_test_result_table], "schema_monitor_test_table", tmp_path / "schema_monitor_test", spark
    )

    test = SchemaMonitor(
        dataset="fap5_incidents",
        monitoring_dataset_path=tmp_path / "schema_monitor_test",
        dataset_path=create_test_data[0],
    )
    test.run(spark)

    assert test.status_code == ResultStatus.success
    assert test.message["schema"] == create_test_data[1]


def test_schema_monitor_no_match(create_test_data, create_result_table, tmp_path, spark):
    previous_test_result_table = create_result_table

    write_to_monitoring_table(
        [previous_test_result_table], "schema_monitor_test_table", tmp_path / "schema_monitor_test", spark
    )

    test = SchemaMonitor(
        dataset="fap5_incidents",
        monitoring_dataset_path=tmp_path / "schema_monitor_test",
        dataset_path=create_test_data[0],
    )
    test.run(spark)

    assert test.status_code == ResultStatus.critical
    assert "critical" in test.message
    assert test.message["schema"] == create_test_data[1]


def test_schema_monitor_not_found(create_test_data, create_result_table, tmp_path, spark):
    previous_test_result_table = create_result_table
    previous_test_result_table.dataset = "foo"
    previous_test_result_table.test_type = "bar"
    previous_test_result_table.test_name = "baz"

    write_to_monitoring_table(
        [previous_test_result_table], "schema_monitor_test_table", tmp_path / "schema_monitor_test", spark
    )

    test = SchemaMonitor(
        dataset="fap5_incidents",
        monitoring_dataset_path=tmp_path / "schema_monitor_test",
        dataset_path=create_test_data[0],
    )
    test.run(spark)

    assert test.status_code == ResultStatus.success
    assert "success" in test.message
    assert test.message["schema"] == create_test_data[1]


def test_schema_monitor_invalid_path(create_test_data, spark):
    tmp_df_path = "foo/bar/"
    test = SchemaMonitor(
        dataset="fap5_incidents", monitoring_dataset_path=tmp_df_path, dataset_path=create_test_data[0]
    )
    test.run(spark)

    assert test.status_code == ResultStatus.error
    assert "error" in test.message
