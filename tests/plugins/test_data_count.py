from datetime import datetime, timedelta, timezone

import pyspark.sql.functions as F
import pytest
from faplytics.core import settings as faplytics_settings
from pyspark.sql import DataFrame

from faplytics_monitoring.core.dataset_utils import (
    ResultModel,
    write_to_monitoring_table,
)
from faplytics_monitoring.core.errors import ResultStatus
from faplytics_monitoring.plugins.data_count import DataCount


@pytest.fixture
def test_data_vin_count(tmp_path, spark):

    data = [
        ("Unknown000000", "2021-04-15T09:48:00.000+0000", False),
        ("1HGCM82633A000000", "2021-04-15T10:48:00.000+0000", True),
        ("3HGCM82633A000000", "2021-04-15T11:48:00.000+0000", False),
        ("UNKNOWN2547162037", "2021-04-16T20:48:00.000+0000", True),
        ("2HGCM82633A000000", "2021-04-16T21:48:00.000+0000", True),
        ("2HGCM82633A000000", "2021-04-16T22:48:00.000+0000", True),
    ]

    df = spark.createDataFrame(data, schema="vin STRING, incident_time STRING, test_vehicle BOOLEAN")
    df = df.withColumn("incident_time", F.to_timestamp(F.col("incident_time")))

    df_path = str(tmp_path / "vin_count_data")
    df.write.format("delta").mode("overwrite").save(df_path)
    return df_path


@pytest.fixture
def test_haf_data_count(tmp_path, spark):
    df = spark.createDataFrame(
        data=[("HAF-DTD", datetime.utcnow()), ("HSM", datetime.utcnow())],
        schema="sub_system STRING, received_date TIMESTAMP",
    )
    df_path = str(tmp_path / "haf_data_count")
    df.write.format("delta").mode("overwrite").save(df_path)
    return df_path


@pytest.fixture
def create_vin_count_datacount(test_data_vin_count, tmp_path):

    return DataCount(
        "fap5_incidents",
        time_column="incident_time",
        monitoring_dataset_path=tmp_path / "data_count_test",
        dataset_path=test_data_vin_count,
        sql="SELECT * FROM df_table WHERE (LOWER(vin) NOT LIKE '%unknown%' AND test_vehicle)",
        drop_duplicates=["vin"],
    )


@pytest.fixture
def create_haf_data_count(test_haf_data_count, tmp_path):
    return DataCount(
        "fap5_incidents",
        time_column="received_date",
        monitoring_dataset_path=tmp_path / "haf_data_count_test",
        dataset_path=test_haf_data_count,
        sql="SELECT * FROM df_table where contains(sub_system,'HAF');",
        count_var="haf_data_count",
    )


def test_vin_count_test_vehicle_success(create_vin_count_datacount, spark):

    test = create_vin_count_datacount
    test.start_datetime = datetime(2021, 4, 15, 0, 0, 0)

    total_vin_count = test._get_data_count(spark=spark)
    assert total_vin_count == 2


def test_haf_data_count_failure(create_haf_data_count, spark):
    test = create_haf_data_count
    haf_data_count = test._get_data_count(spark=spark)
    assert haf_data_count == 1
    test._set_result_code(
        data_count=haf_data_count, prev_runs_info={"prev_data_count": 1, "current_rank": 1, "min": 0, "max": 2}
    )
    assert test.status_code == ResultStatus.success


def test_vin_count_success(create_vin_count_datacount, spark):

    test = create_vin_count_datacount
    test.sql = "SELECT * FROM df_table WHERE (LOWER(vin) NOT LIKE '%unknown%' AND NOT test_vehicle)"
    test.start_datetime = datetime(2021, 4, 15, 0, 0, 0)

    total_vin_count = test._get_data_count(spark=spark)
    assert total_vin_count == 1


def test_vin_count_in_chosen_timeframe_success(create_vin_count_datacount, spark):

    test = create_vin_count_datacount
    test.start_datetime = datetime(2021, 4, 16, 0, 0, 0)

    total_vin_count = test._get_data_count(spark=spark)
    assert total_vin_count == 1


def test_data_count_post_init(tmp_path):
    datelike = DataCount(
        "fap4_events",
        time_column="backend_received_date",
        monitoring_dataset_path=tmp_path / "data_count_test",
        duration_hours=12,
    )
    assert isinstance(datelike.start_datetime, datetime)
    assert datelike.start_datetime < datetime.now(timezone.utc) - timedelta(hours=12)


def test_data_count_fail(local_faplytics_config, tmp_path, spark):
    faplytics_settings.load_file(str(local_faplytics_config))

    # test fail of reading of previous result:
    test = DataCount(
        "fap4_events", time_column="backend_received_date", monitoring_dataset_path=tmp_path / "data_count_test"
    )
    test.start_datetime = datetime(2021, 1, 1, 0, 0, 0)

    total_daily_count = test._get_data_count(spark=spark)
    assert total_daily_count == 18
    test.run(spark)

    assert "error" in test.message  # type: ignore


def test_data_count_0(local_faplytics_config, tmp_path, spark):
    faplytics_settings.load_file(str(local_faplytics_config))

    # Test to produce an error if no data point was received
    test = DataCount(
        "fap4_events", time_column="backend_received_date", monitoring_dataset_path=tmp_path / "data_count_test"
    )
    test.start_datetime = datetime(2030, 1, 1, 0, 0, 0)

    total_daily_count = test._get_data_count(spark=spark)
    assert total_daily_count == 0
    test.run(spark)

    assert test.status_code == ResultStatus.error  # type: ignore


def test_data_count_with_threshold(local_faplytics_config, tmp_path, spark):
    faplytics_settings.load_file(str(local_faplytics_config))

    # Test to produce a success if no data point was received and a threshold was set
    test = DataCount(
        "fap4_events", time_column="backend_received_date", monitoring_dataset_path=tmp_path / "data_count_test"
    )
    test.start_datetime = datetime(2030, 1, 1, 0, 0, 0)
    test.alert_threshold = 1

    total_daily_count = test._get_data_count(spark=spark)
    assert total_daily_count == 0
    test.run(spark)

    assert test.status_code == ResultStatus.success  # type: ignore


def test_data_count_functions_individually(local_faplytics_config, tmp_path, spark):
    faplytics_settings.load_file(str(local_faplytics_config))

    # with testdata oder than 1 day
    previous_test_result_table = [
        ResultModel(
            time=datetime.utcnow() - timedelta(hours=24 * i),
            dataset="fap4_events",
            test_type="data_count",
            test_name="data_count",
            test_group="basic",
            test_result_code=0,
            test_result_map={"data_count": f"{1 if i < 3 else 2}"},
            html="",
        )
        for i in range(5)
    ]

    write_to_monitoring_table(previous_test_result_table, "data_count_testtable", tmp_path / "data_count_test", spark)

    test = DataCount(
        "fap4_events",
        time_column="backend_received_date",
        monitoring_dataset_path=tmp_path / "data_count_test",
        iqr_multiplier=1.5,
    )
    test.start_datetime = datetime(2021, 1, 1, 0, 0, 0)

    results_table = test._retrieve_prev_results(spark=spark, last_n_days=0)
    assert isinstance(results_table, DataFrame)
    assert results_table.count() == 0

    results_table = test._retrieve_prev_results(spark=spark)
    bounds = test._calc_IQR(results_table)
    assert bounds[0] == 1
    assert bounds[1] == 2

    test._set_result_code(data_count=10, prev_runs_info="error")
    assert "error" in test.message  # type: ignore

    test._set_result_code(
        data_count=100,
        prev_runs_info={"prev_data_count": 1, "current_rank": 1, "min": bounds[0], "max": bounds[1]},
    )
    assert test.status_code == ResultStatus.error
    assert "outlier" in test.message  # type: ignore


def test_data_count_success(tmp_path, spark):

    previous_test_result_table = [
        ResultModel(
            time=datetime.utcnow() - timedelta(hours=24 * d) - timedelta(seconds=s),
            dataset="fap4_events",
            test_type="data_count",
            test_name="data_count",
            test_group="basic",
            test_result_code=0,
            test_result_map={"data_count": f"{5*d}"},
            html="",
        )
        for d in range(5)
        for s in (0, 1)
    ]

    write_to_monitoring_table(
        previous_test_result_table[1:], "data_count_testtable", tmp_path / "data_count_test", spark
    )
    test = DataCount(
        dataset="fap4_events",
        dataset_path="tests/testdata/fap4_events/data_with_date",
        time_column="backend_received_date",
        monitoring_dataset_path=tmp_path / "data_count_test",
        iqr_range_days=100_000,
        iqr_multiplier=1.5,
    )
    test.start_datetime = datetime(2021, 1, 1, 0, 0, 0)
    test.run(spark=spark)
    assert test.status_code == ResultStatus.success
