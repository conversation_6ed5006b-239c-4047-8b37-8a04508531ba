import datetime

from faplytics.core import settings as faplytics_settings
from pyspark.sql import DataFrame
from pyspark.sql import functions as F

from faplytics_monitoring.plugins.incident_burst_int import IncidentBurstInt


class DatasetMock:
    def __init__(self, df: DataFrame):
        self.df = df

    def read(self) -> DataFrame:
        return self.df


def test_incident_burst(mocker, local_faplytics_config, spark):
    # mock current time to be within X minutes of last update

    faplytics_settings.load_file(str(local_faplytics_config))
    mock_datetime = mocker.patch("faplytics_monitoring.plugins.incident_burst_int.datetime")
    mock_datetime.utcnow.return_value = datetime.datetime(2022, 11, 11, 12)

    df = spark.createDataFrame(
        [("2022-11-11 11:15:00", "VIN1", "BURST_INC", 415) for _ in range(1001)]
        + [("2022-11-11 12:15:00", "VIN2", "NORMAL_INC", 420) for _ in range(999)],
        schema="incident_time string, vin string, incident_name string, ihd_version int",
    )
    df = df.withColumn("incident_time", F.to_date("incident_time"))
    mock_ds = mocker.patch("faplytics_monitoring.plugins.incident_burst_int.Dataset")
    mock_ds.for_name.return_value = DatasetMock(df)

    test = IncidentBurstInt("fap5_incidents")
    test.run(spark)
    assert test.message == {
        "incident_burst_threshold": "1000",
        "num_bursts": "1",
        "#incidents_in_biggest_bursts": "1001",
        "affected_vins": "['VIN1']",
        "affected_ihd_versions": "[415]",
    }

    df = spark.createDataFrame(
        [("2022-11-11 11:15:00", "VIN1", "NORMAL_INC", 415) for _ in range(10)]
        + [("2022-11-11 12:15:00", "VIN2", "NORMAL_INC", 420) for _ in range(999)],
        schema="incident_time string, vin string, incident_name string, ihd_version int",
    )
    df = df.withColumn("incident_time", F.to_date("incident_time"))
    mock_ds.for_name.return_value = DatasetMock(df)

    test = IncidentBurstInt("fap5_incidents")
    test.run(spark)
    assert test.message == {"incident_burst_threshold": "1000", "num_bursts": "0"}
