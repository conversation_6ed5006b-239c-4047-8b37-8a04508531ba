from datetime import datetime

import pytest
from pyspark.sql.functions import col, to_timestamp

from faplytics_monitoring.core.errors import ResultStatus
from faplytics_monitoring.plugins.oso_data_count import OsoDataCount


@pytest.fixture
def data_oso_count(tmp_path, spark):

    data = [
        ("445", "I_FAILED_OSO_VALIDATION_GPS", "HAF-HMHQ", "2023-10-29", "2023-10-29T03:45:00.000+0000"),
        ("445", "I_FAILED_OSO_VALIDATION_GAP_OVERLAP", "HAF-HMHQ", "2023-10-29", "2023-10-29T03:45:00.000+0000"),
        ("471", "I_FAILED_OSO_VALIDATION_GPS", "HAF-HMHQ", "2023-10-29", "2023-10-29T03:45:00.000+0000"),
        ("445", "I_FAILED_OSO_VALIDATION_GAP_OVERLAP", "HAF-HMHQ", "2023-10-29", "2023-10-29T03:45:00.000+0000"),
        ("445", "I_FAILED_OSO_VALIDATION_GAP_OVERLAP", "CAQ", "2023-10-29", "2023-10-29T03:45:00.000+0000"),
        ("445", "I_FAILED_OSO_VALIDATION_GAP_OVERLAP", "HAF-HMHQ", "2021-10-29", "2021-10-29T03:45:00.000+0000"),
    ]

    df = spark.createDataFrame(data, ["ihd_version", "incident_name", "sub_system", "date", "incident_time"])
    df = df.withColumn("incident_time", to_timestamp(col("incident_time")))
    df_path = str(tmp_path / "oso_count_data")
    df.write.format("delta").mode("overwrite").save(df_path)

    return df_path


@pytest.fixture
def data_oso_warning_count(tmp_path, spark):

    data = [
        ("445", "I_FAILED_OSO_VALIDATION_GPS", "HAF-HMHQ", "2023-10-29", "2023-10-29T03:45:00.000+0000"),
        ("445", "I_FAILED_OSO_VALIDATION_GAP_OVERLAP", "HAF-HMHQ", "2023-10-29", "2023-10-29T03:45:00.000+0000"),
    ] * 100
    df = spark.createDataFrame(
        data, ["ihd_version", "incident_name", "sub_system", "date", "incident_time"]
    ).withColumn("incident_time", to_timestamp(col("incident_time")))
    df_path_for_threshold = str(tmp_path / "oso_count_data")
    df.write.format("delta").mode("overwrite").save(df_path_for_threshold)

    return df_path_for_threshold


def test_oso_data_count(data_oso_count, tmp_path, spark):

    test = OsoDataCount(
        "fap5_incidents",
        time_column="incident_time",
        monitoring_dataset_path=tmp_path / "data_count_test",
        dataset_path=data_oso_count,
    )
    test.start_datetime = datetime(2023, 10, 29, 0, 0, 0)

    total_daily_count = test._get_total_daily_oso_count(spark=spark).count()
    assert total_daily_count == 3
    test.run(spark)
    assert test.status_code == ResultStatus.success


def test_oso_data_count_thre_exceed_error(data_oso_warning_count, tmp_path, spark):

    test_2 = OsoDataCount(
        "fap5_incidents",
        time_column="incident_time",
        monitoring_dataset_path=tmp_path / "data_count_test",
        dataset_path=data_oso_warning_count,
    )
    test_2.start_datetime = datetime(2023, 10, 29, 0, 0, 0)

    total_daily_count = test_2._get_total_daily_oso_count(spark=spark).count()
    assert total_daily_count == 200
    test_2.run(spark)
    assert test_2.status_code == ResultStatus.error


def test_oso_data_sucess(data_oso_warning_count, tmp_path, spark):

    test_3 = OsoDataCount(
        "fap5_incidents",
        time_column="incident_time",
        monitoring_dataset_path=tmp_path / "data_count_test",
        dataset_path=data_oso_warning_count,
    )
    test_3.start_datetime = datetime(2027, 10, 29, 0, 0, 0)

    total_daily_count = test_3._get_total_daily_oso_count(spark=spark).count()
    assert total_daily_count == 0
    test_3.run(spark)
    assert test_3.status_code == ResultStatus.success
