from datetime import datetime, timedelta

import pytest
from faplytics.core import settings as faplytics_settings

from faplytics_monitoring.core.errors import ResultStatus
from faplytics_monitoring.plugins.last_modified import LastModified


def test_last_modified(mocker, local_faplytics_config, spark):
    # mock current time to be within X minutes of last update

    faplytics_settings.load_file(str(local_faplytics_config))

    test = LastModified("fap4_events")

    _, time_of_last_update = test.get_time_of_last_update(spark)
    # test success
    mock_datetime = mocker.patch("faplytics_monitoring.plugins.last_modified.datetime")
    mock_datetime.now.return_value = time_of_last_update + timedelta(minutes=119)
    test = LastModified("fap4_events")
    test.run(spark)
    assert test.status_code == ResultStatus.success

    # test as deltatable
    test = LastModified(dataset="fap4_events_from_delta", dataset_path="tests/testdata/fap4_events/data/")
    test.run(spark)
    assert test.status_code == ResultStatus.success

    # test warning
    mock_datetime.now.return_value = time_of_last_update + timedelta(minutes=179)
    test = LastModified("fap4_events")
    test.run(spark)
    assert test.status_code == ResultStatus.warning

    # test error
    mock_datetime.now.return_value = time_of_last_update + timedelta(minutes=239)
    test = LastModified("fap4_events")
    test.run(spark)
    assert test.status_code == ResultStatus.error

    # test critical error
    mock_datetime.now.return_value = time_of_last_update + timedelta(minutes=241)
    test = LastModified("fap4_events")
    test.run(spark)
    assert test.status_code == ResultStatus.critical

    # test pass threshold times
    test = LastModified("fap4_events", success_threshold_mins=250, warning_threshold_mins=251, error_threshold_mins=252)
    test.run(spark)
    assert test.status_code == ResultStatus.success

    # test whether thresholds are checked
    with pytest.raises(ValueError):
        LastModified("fap4_events", success_threshold_mins=250)


def test_last_modified_with_timecolumn(spark):

    last_modified = LastModified(
        dataset="fap4_events_from_delta",
        dataset_path="tests/testdata/fap4_events/data/",
        time_column="backend_process_date",
    )

    # run the test
    last_modified.run(spark)

    # check the results
    assert last_modified.status_code == ResultStatus.critical  # should be crititcal
    assert last_modified.message["time_of_latest_datapoint"] == "2021-06-28 20:52:05.712000+00:00"  # type: ignore
    assert float(last_modified.message["minutes_to_latest_datapoint"]) > 525600  # type: ignore

    # test with not existing parquet
    latest_dp, earliest_dp = last_modified.get_latest_datapoint(["this_file_is_madeup.parquet"], spark)
    assert isinstance(latest_dp, datetime)
    assert str(latest_dp) == "2021-06-28 20:52:05.712000+00:00"
    assert earliest_dp is None
