import datetime

from faplytics.core import settings as faplytics_settings
from pyspark.sql import DataFrame
from pyspark.sql import functions as F
from pyspark.sql import types as T

from faplytics_monitoring.core.errors import ResultStatus
from faplytics_monitoring.plugins.session_hash_check import (
    KnownIssue,
    SessionHashCheck,
    SessionHashCheckConfig,
)


class DatasetMock:
    def __init__(self, df: DataFrame):
        self.df = df

    def read(self) -> DataFrame:
        return self.df


class DateMock(datetime.date):
    @classmethod
    def today(cls):
        return cls(2011, 11, 11)


SCHEMA = T.StructType(
    [
        T.StructField("received_date", T.StringType(), True),
        <PERSON>.<PERSON>(
            "sessionHashList",
            T.ArrayType(T.StringType(), True),
        ),
        T.<PERSON>ruct<PERSON>(
            "incidents",
            T.ArrayType(T.StructType([<PERSON><PERSON><PERSON><PERSON><PERSON>("session", T.IntegerType(), True)]), True),
        ),
    ]
)


def get_df_ok(spark):
    return spark.createDataFrame(
        [
            (
                "2011-11-11",
                ["hash1", "hash2", "hash2", "hash3"],
                [{"session": 1}, {"session": 2}, {"session": 2}, {"session": 3}],
            ),
            (
                "2011-11-11",
                ["hash4", "hash4", "hash5", "hash6"],
                [{"session": 4}, {"session": 4}, {"session": 5}, {"session": 6}],
            ),
        ],
        schema=SCHEMA,
    )


def get_df_errors(spark):
    return spark.createDataFrame(
        [
            (
                "2011-11-11",
                ["hash1", "hash2", "hash2", "hash3"],
                [{"session": 1}, {"session": 2}, {"session": 2}, {"session": 3}],
            ),
            (
                "2011-11-11",
                ["hash4", "hash4", "hash5", "hash6"],
                [{"session": 4}, {"session": 5}, {"session": 4}, {"session": 6}],
            ),
            (
                "2011-11-11",
                ["hash4", "hash4", "hash5", "hash6"],
                [{"session": 4}, {"session": 4}, {"session": 4}, {"session": 6}],
            ),
        ],
        schema=SCHEMA,
    )


def mock_date_today(mocker):
    return mocker.patch("faplytics_monitoring.plugins.session_hash_check.date", DateMock)


def mock_ds(mocker, df):
    df = df.withColumn("received_date", F.to_date("received_date"))
    mock_ds = mocker.patch("faplytics_monitoring.plugins.session_hash_check.Dataset")
    mock_ds.for_name.return_value = DatasetMock(df)
    return mock_ds


def mock_config(mocker, end_date):
    return mocker.patch(
        "faplytics_monitoring.plugins.session_hash_check.CONFIG",
        SessionHashCheckConfig([KnownIssue("no_pad", "", end_date)]),
    )


def test_session_hash_check_ok(mocker, local_faplytics_config, spark):
    faplytics_settings.load_file(str(local_faplytics_config))
    df = get_df_ok(spark)
    mock_ds(mocker, df)
    mock_date_today(mocker)
    mock_config(mocker, end_date="2011-11-12")

    test = SessionHashCheck("fap5_incidents_decoded")
    test.run(spark)
    assert test.status_code == ResultStatus.success
    assert test.message == None


def test_session_hash_check_known_issue(mocker, local_faplytics_config, spark):
    faplytics_settings.load_file(str(local_faplytics_config))
    df = get_df_errors(spark)
    mock_ds(mocker, df)
    mock_date_today(mocker)
    mock_config(mocker, end_date="2011-11-12")

    test = SessionHashCheck("fap5_incidents_decoded")
    test.run(spark)
    assert test.status_code == ResultStatus.warning
    assert test.message == {
        "error_msgs": """\
[\
"Found 2 out of 3 R4R messages with different hashes for same session.", \
"Found 1 out of 3 R4R messages with different sessions for same hash.", \
"Known issue: no_pad."\
]"""
    }


def test_session_hash_check_unknown_error(mocker, local_faplytics_config, spark):
    faplytics_settings.load_file(str(local_faplytics_config))
    df = get_df_errors(spark)
    mock_ds(mocker, df)
    mock_date_today(mocker)
    mock_config(mocker, end_date="2011-11-10")

    test = SessionHashCheck("fap5_incidents_decoded")
    test.run(spark)
    assert test.status_code == ResultStatus.critical
    assert test.message == {
        "error_msgs": """\
[\
"Found 2 out of 3 R4R messages with different hashes for same session.", \
"Found 1 out of 3 R4R messages with different sessions for same hash."\
]"""
    }
