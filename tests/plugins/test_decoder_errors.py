import datetime

from faplytics.core import settings as faplytics_settings
from pyspark.sql import DataFrame
from pyspark.sql import functions as F
from pyspark.sql import types as T

from faplytics_monitoring.core.errors import ResultStatus
from faplytics_monitoring.plugins.decoder_errors import DecoderErrors


class DatasetMock:
    def __init__(self, df: DataFrame):
        self.df = df

    def read(self) -> DataFrame:
        return self.df


def test_incident_decoder_errors(mocker, local_faplytics_config, spark):
    # mock current time to be within X minutes of last update

    faplytics_settings.load_file(str(local_faplytics_config))
    mock_datetime = mocker.patch("faplytics_monitoring.plugins.decoder_errors.date")
    mock_datetime.today.return_value = datetime.date(2022, 11, 11)
    schema = T.StructType(
        [
            T.Struct<PERSON>ield("received_date", T.StringType(), True),
            <PERSON><PERSON><PERSON><PERSON><PERSON>("ihd_version", T.IntegerType(), True),
            <PERSON><PERSON>(
                "decoding_exceptions",
                T.ArrayType(T.StructType([T.<PERSON>ruct<PERSON>ield("message", T.StringType(), True)]), True),
            ),
            T.StructField("incidents", T.ArrayType(T.StringType()), True),
        ]
    )

    df = spark.createDataFrame(
        [
            (
                "2022-11-11 11:15:00",
                415,
                [{"message": "Could not find incident ID 0"}, {"message": "Could not find incident ID 0"}],
                ["inc1", "inc2"],
            ),  # unknown error
            (
                "2022-11-11 11:15:00",
                455,
                [{"message": "Could not find incident ID 0"}, {"message": "Could not find incident ID 0"}],
                ["inc1", "inc2"],
            ),  # known error
            *[
                (
                    "2022-11-11 11:15:00",
                    455,
                    [{"message": "Incident from future"}, {"message": "Incident from future"}],
                    [],
                )
                for _ in range(100)  # number of unknown error exceed unknown_error_threshold
            ],
            ("2022-11-11 12:15:00", 455, [], []),  # empty R4R
            ("2022-11-11 12:15:00", 465, [], []),  # empty R4R
            ("2022-11-11 12:15:00", 470, [], ["inc1", "inc2"]),  # ok
        ],
        schema=schema,
    )
    df = df.withColumn("received_date", F.to_date("received_date"))
    mock_ds = mocker.patch("faplytics_monitoring.plugins.decoder_errors.Dataset")
    mock_ds.for_name.return_value = DatasetMock(df)

    test = DecoderErrors("fap5_incidents_decoded")
    test.run(spark)
    assert test.status_code == ResultStatus.critical
    assert test.message == {
        "unknown_errors_count": "103",
        "unknown_errors_affected_ihd_versions": "[415, 455, 465]",
        "unknown_errors_msgs": '["Could not find incident ID 0", "Incident from future", "empty"]',
        "known_errors_count": "1",
        "known_errors_affected_ihd_versions": "[455]",
        "known_errors_msgs": '["id0_bursts"]',
    }

    df = spark.createDataFrame(
        [
            (
                "2022-11-11 11:15:00",
                455,
                [{"message": "Could not find incident ID 0"}, {"message": "Could not find incident ID 0"}],
                ["inc1", "inc2"],
            ),  # known error
            ("2022-11-11 12:15:00", 470, [], ["inc1", "inc2"]),  # ok
        ],
        schema=schema,
    )
    df = df.withColumn("received_date", F.to_date("received_date"))
    mock_ds = mocker.patch("faplytics_monitoring.plugins.decoder_errors.Dataset")
    mock_ds.for_name.return_value = DatasetMock(df)
    test.run(spark)
    assert test.status_code == ResultStatus.warning
    assert test.message == {
        "known_errors_count": "1",
        "known_errors_affected_ihd_versions": "[455]",
        "known_errors_msgs": '["id0_bursts"]',
    }

    df = spark.createDataFrame(
        [
            ("2022-11-11 12:15:00", 470, [], ["inc1", "inc2"]),  # ok
        ],
        schema=schema,
    )
    df = df.withColumn("received_date", F.to_date("received_date"))
    mock_ds = mocker.patch("faplytics_monitoring.plugins.decoder_errors.Dataset")
    mock_ds.for_name.return_value = DatasetMock(df)
    test.run(spark)
    assert test.status_code == ResultStatus.success
    assert test.message == None


def test_histogram_decoder_errors(mocker, local_faplytics_config, spark):
    # mock current time to be within X minutes of last update

    faplytics_settings.load_file(str(local_faplytics_config))
    mock_datetime = mocker.patch("faplytics_monitoring.plugins.decoder_errors.date")
    mock_datetime.today.return_value = datetime.date(2022, 11, 11)
    schema = T.StructType(
        [
            T.StructField("received_date", T.StringType(), True),
            T.StructField("ihd_version", T.IntegerType(), True),
            T.StructField(
                "decoding_exceptions",
                T.ArrayType(T.StructType([T.StructField("message", T.StringType(), True)]), True),
            ),
            T.StructField("histograms", T.ArrayType(T.StringType()), True),
        ]
    )

    df = spark.createDataFrame(
        [
            *tuple([("2022-11-11 12:15:00", 470, [], [])] * 10001),  # above absolute tolerance
            *tuple([("2022-11-11 12:15:00", 480, [], [])] * 10001),
            *tuple([("2022-11-11 12:15:00", 480, [], ["hist"])] * 90000),  # above relative tolerance
        ],
        schema=schema,
    )
    df = df.withColumn("received_date", F.to_date("received_date"))
    mock_ds = mocker.patch("faplytics_monitoring.plugins.decoder_errors.Dataset")
    mock_ds.for_name.return_value = DatasetMock(df)

    test = DecoderErrors("fap5_histograms_decoded")
    test.run(spark)
    assert test.status_code == ResultStatus.critical
    assert test.message == {
        "unknown_errors_count": "20002",
        "unknown_errors_affected_ihd_versions": "[470, 480]",
        "unknown_errors_msgs": '["empty"]',
    }

    df = spark.createDataFrame(
        [
            *tuple([("2022-11-11 12:15:00", 465, [], [])] * 9999),  # below absolute tolerance
            *tuple([("2022-11-11 12:15:00", 475, [], [])] * 10001),
            *tuple([("2022-11-11 12:15:00", 475, [], ["hist"])] * 1000000),  # below relative tolerance
        ],
        schema=schema,
    )
    df = df.withColumn("received_date", F.to_date("received_date"))
    mock_ds = mocker.patch("faplytics_monitoring.plugins.decoder_errors.Dataset")
    mock_ds.for_name.return_value = DatasetMock(df)

    test = DecoderErrors("fap5_histograms_decoded")
    test.run(spark)
    assert test.status_code == ResultStatus.warning
    assert test.message == {
        "known_errors_count": "20000",
        "known_errors_affected_ihd_versions": "[465, 475]",
        "known_errors_msgs": '["empty"]',
    }

    df = spark.createDataFrame(
        [
            ("2022-11-11 12:15:00", 485, [], ["inc1", "inc2"]),  # ok
        ],
        schema=schema,
    )
    df = df.withColumn("received_date", F.to_date("received_date"))
    mock_ds = mocker.patch("faplytics_monitoring.plugins.decoder_errors.Dataset")
    mock_ds.for_name.return_value = DatasetMock(df)

    test = DecoderErrors("fap5_histograms_decoded")
    test.run(spark)
    assert test.status_code == ResultStatus.success
    assert test.message == None
