from pathlib import Path

from faplytics_monitoring.core import factory
from faplytics_monitoring.core.loader import load_plugins
from faplytics_monitoring.main import DataTester


def test_config_sanity():
    """
    this tests if the config files is valid, plugins are loaded and initalized
    """
    for config_file_name in ["prod.yaml", "int.yaml", "prod_testing.yaml"]:

        config = DataTester.load_testing_config(
            config_file_name, Path(__file__).parent.parent.resolve() / "faplytics_monitoring"
        )
        load_plugins(config["plugins"])
        for dataset in config["datasets"]:
            test_configs: list = config["datasets"][dataset]
            for item in test_configs:
                factory.create(item, dataset)
