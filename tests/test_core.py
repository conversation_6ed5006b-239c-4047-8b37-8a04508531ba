import pytest
from faplytics.core import settings as faplytics_settings
from pyspark.sql.dataframe import DataFrame
from pyspark.sql.types import StructType

from faplytics_monitoring.core.dataset_utils import (
    ResultModel,
    show_monitoring_table,
    write_to_monitoring_table,
)
from faplytics_monitoring.core.loader import load_plugins
from faplytics_monitoring.main import DataTester


def test_dataframe(spark):
    schema = StructType.fromJson(ResultModel.spark_schema())
    df = spark.createDataFrame(data=[], schema=schema)
    print(df.schema)
    assert isinstance(df, DataFrame)


def test_pluging_loading_and_template(spark, template_testing_config, local_faplytics_config):

    faplytics_settings.load_file(str(local_faplytics_config))

    load_plugins(template_testing_config["plugins"])

    data_tester = DataTester(config=template_testing_config, spark=spark)
    for dataset in template_testing_config["datasets"]:
        data_tester.run_data_tests(dataset)
    results = data_tester.run_data_tests("fap4_events")
    assert len(results) == 1
    assert results[0].test_result_code == 0
    assert results[0].test_result_map["data_count"] == "140"  # type: ignore


def test_sample_config_typo(spark, template_testing_config):
    template_testing_config["datasets"]["fap4_events"][0]["type"] = "typo"
    with pytest.raises(ValueError) as e_info:

        load_plugins(template_testing_config["plugins"])
        data_tester = DataTester(config=template_testing_config, spark=spark)
        for dataset in template_testing_config["datasets"]:
            data_tester.run_data_tests(dataset)
        assert e_info == "unknown test type typo"


def test_output_table(mocker, sample_test_result, spark, tmp_path):

    write_to_monitoring_table(sample_test_result, "test_output", str(tmp_path / "test_output"), spark)
    df_written = spark.sql("select * from test_output")

    df_from_show = show_monitoring_table(sample_test_result, spark)

    assert df_written.columns == df_from_show.columns
    assert df_written.count() == df_from_show.count() == 4
