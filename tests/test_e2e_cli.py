import pytest
from faplytics.core import settings as faplytics_settings

from faplytics_monitoring.cli import cli_runner


@pytest.fixture()
def cli_mocks(mocker, spark, template_testing_config, local_faplytics_config):
    spark_mock = mocker.patch("faplytics_monitoring.main.get_spark_session")
    spark_mock.return_value = spark

    test_config_mock = mocker.patch("faplytics_monitoring.main.DataTester.load_testing_config")
    test_config_mock.return_value = template_testing_config

    config_file_mock = mocker.patch("faplytics_monitoring.main.faplytics_settings")
    config_file_mock.load_file.side_effect = faplytics_settings.load_file(str(local_faplytics_config))


def test_cli_wrapper(capsys):
    cli_runner(["--help"])
    captured = capsys.readouterr()
    assert "--testing / --no-testing" in captured.out


def test_run_cli(cli_mocks):
    cli_runner(
        [
            "--testing",
            "run",
            "--debug",
            "--show-result",
        ]
    )


def test_notify_cli(cli_mocks, sample_test_result_saved):
    cli_runner(["notify"])


def test_digest_cli(cli_mocks, sample_test_result_saved):
    cli_runner(["digest"])
