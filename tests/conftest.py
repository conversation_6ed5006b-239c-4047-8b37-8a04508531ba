import datetime
import os
import re
import shutil

import pytest
from sedona.spark import SedonaContext

from faplytics_monitoring.core.dataset_utils import write_to_monitoring_table


def pytest_configure():
    from faplytics.plugins import discover_plugins

    discover_plugins()


@pytest.fixture
def spark():

    extra_java_options = ""
    if os.getenv("HTTP_PROXY"):
        proxy = re.sub("^https?://", "", os.getenv("HTTP_PROXY"))  # type: ignore
        host, port = proxy.split(":")
        extra_java_options = (
            f" -Dhttp.proxyHost={host} -Dhttp.proxyPort={port} -Dhttps.proxyHost={host} -Dhttps.proxyPort={port}"
        )

    config = (
        SedonaContext.builder()
        .master("local[4]")
        .appName("monitoring_test")
        .config("spark.default.parallelism", "5")
        .config("spark.sql.shuffle.partitions", "1")
        .config(
            "spark.jars.packages",
            "io.delta:delta-spark_2.12:3.2.0,"
            "org.apache.sedona:sedona-spark-3.0_2.12:1.6.0,"
            "org.datasyslab:geotools-wrapper:1.6.0-31.0",
        )
        .config("spark.sql.extensions", "io.delta.sql.DeltaSparkSessionExtension")
        .config("spark.sql.catalog.spark_catalog", "org.apache.spark.sql.delta.catalog.DeltaCatalog")
        .config("spark.driver.extraJavaOptions", extra_java_options)
        .getOrCreate()
    )
    spark = SedonaContext.create(config)
    spark.sparkContext.setLogLevel("ERROR")
    os.environ["SKIP_VACUUM"] = "1"

    # cleanup tables before each test
    for row in spark.sql("SHOW TABLE EXTENDED LIKE '*'").collect():
        information = {
            line.split(":", 1)[0].strip(): line.split(":", 1)[1].strip()
            for line in row["information"].splitlines()
            if line.strip()
        }
        loc = information.get("Location")
        if loc and loc.startswith("file:/"):
            shutil.rmtree(loc[len("file:") :])
        spark.sql(f"DROP TABLE {row['tableName']}").collect()
    return spark


@pytest.fixture(autouse=True)
def mock_timestamp_now(mocker):
    mock = mocker.MagicMock()
    mock.return_value = datetime.datetime(1970, 1, 1)
    mocker.patch("faplytics_monitoring.main.DataTester.timestamp_now", mock)


@pytest.fixture
def sample_test_result():
    from faplytics_monitoring.core.dataset_utils import ResultModel

    return [
        ResultModel(
            dataset=f"dataset_{code}",
            test_type="data_count",
            test_name="data_count",
            test_group="basic",
            test_result_code=code,
            test_result_map={},
            html="",
        )
        for code in range(4)
    ]


@pytest.fixture
def sample_test_result_saved(sample_test_result, template_testing_config, spark):
    defs = template_testing_config["definitions"]
    write_to_monitoring_table(sample_test_result, defs["sql_table_name"], defs["sql_dataset_path"], spark)


@pytest.fixture(autouse=True, scope="session")
def webhook_url():
    os.environ["WEBHOOK_URL"] = "print"


@pytest.fixture
def send_mock(mocker):
    send_mock = mocker.MagicMock(name="send_mock")
    mocker.patch("faplytics_monitoring.core.notification.NotificationMessage.send", new=send_mock)
    yield send_mock


@pytest.fixture
def datetime_now_night(mocker):
    datetime_now_night = mocker.MagicMock(name="datetime")
    mocker.patch("faplytics_monitoring.core.notification.datetime", new=datetime_now_night)
    yield datetime_now_night


@pytest.fixture
def template_testing_config(tmp_path):

    # spark will be global for the complete session
    # so we'll have to
    table_name = "results_" + tmp_path.name.replace("-", "_")
    return {
        "env": "int",
        "plugins": ["template_test"],
        "datasets": {
            "fap4_events": [
                {
                    "type": "template_test",
                    "name": "template_test",
                    "group": "basic",
                    "parameters": {"parameter_line_count": 5},
                }
            ]
        },
        "definitions": {"sql_table_name": table_name, "sql_dataset_path": str(tmp_path / "results")},
    }


@pytest.fixture
def local_faplytics_config(tmp_path_factory):
    from pathlib import Path

    import yaml

    with open((Path(__file__).resolve().parent / "faplytics_test_settings.yaml")) as f:
        settings = yaml.safe_load(f)
    settings["default"]["DATA_FRAMES"]["root_path"] = str(Path(__file__).resolve().parent / "testdata")
    out = tmp_path_factory.mktemp("config") / "faplytics_test_conf.yaml"
    with open(out, "w") as outfile:

        yaml.safe_dump(settings, outfile)
    return out
