from datetime import datetime
from pathlib import Path

import pyspark.sql
import pytest
from faplytics.exceptions import FaplyticsError
from faplytics.pipelines import Dataset
from pyspark.sql.utils import AnalysisException

from faplytics_monitoring.core.datatest import FaplyticsDataTest
from faplytics_monitoring.core.errors import ResultStatus
from faplytics_monitoring.main import DataTester, ResultModel


@pytest.fixture
def mock_config_file(tmpdir):
    mock_config_file = tmpdir.join("config_prod.yaml")
    mock_config_file.write("datasets: []\nplugins: []")
    return mock_config_file


@pytest.fixture
def mock_dataset(mocker):
    mock_dataset = mocker.MagicMock(spec=Dataset)
    mock_dataset.read.return_value = None
    return mock_dataset


@pytest.fixture
def mock_faplytics_error():
    return FaplyticsError("This is a FaplyticsError")


@pytest.fixture
def mock_analysis_exception(mocker):
    return AnalysisException("This is an AnalysisException", stackTrace="testing")


def test_load_testing_config(mock_config_file):
    # Test when the config file exists and has the required keys
    config = DataTester.load_testing_config(str(mock_config_file), root_path=Path(mock_config_file.dirpath()))
    assert config == {"datasets": [], "plugins": []}

    # Test when the config file does not exist
    with pytest.raises(FileNotFoundError):
        DataTester.load_testing_config("ivalid.yaml", root_path=Path(mock_config_file.dirpath()))

    # Test when the config file does not have the required keys
    mock_config_file.write("invalid: []")
    with pytest.raises(AssertionError):
        DataTester.load_testing_config(str(mock_config_file), root_path=Path(mock_config_file.dirpath()))


def test_run_data_tests(mocker, spark):
    # Set up test data and mock objects
    dataset = "test_dataset"
    config = {"datasets": {"test_dataset": [{"type": "foo", "name": "bar", "group": "baz", "parameters": {}}]}}
    mock_test = mocker.MagicMock(spec=FaplyticsDataTest)
    mock_test.run.return_value = None
    mock_test.status_code = ResultStatus.success
    mock_test.message = {"test_key": "test_value"}
    mock_test.html = "test_html"
    mock_factory = mocker.MagicMock()
    mock_factory.return_value = mock_test

    expected_result = [
        ResultModel(
            dataset=dataset,
            test_type="foo",
            test_name="bar",
            test_group="baz",
            test_result_code=ResultStatus.success.value,
            test_result_map={"test_key": "test_value"},
            html="test_html",
        )
    ]

    # Test the run_data_tests function
    mocker.patch("faplytics_monitoring.core.factory.create", mock_factory)
    data_tester = DataTester(config=config, spark=spark)
    result = data_tester.run_data_tests(dataset)
    assert result == expected_result


def test_run_data_test_with_failing_run(mocker):
    # Test a failing test
    dataset = "test_dataset"
    config = {"datasets": {"test_dataset": [{"type": "foo", "name": "bar", "group": "baz", "parameters": {}}]}}
    mock_test = mocker.MagicMock(spec=FaplyticsDataTest)
    mock_test.run.side_effect = Exception("mocked error")
    mock_test.status_code = ResultStatus.error
    mock_test.html = None
    mock_factory = mocker.MagicMock()
    mock_factory.return_value = mock_test

    expected_result = [
        ResultModel(
            dataset=dataset,
            test_type="foo",
            test_name="bar",
            test_group="baz",
            test_result_code=ResultStatus.error.value,
            test_result_map={"run failed": "mocked error"},
            html=None,
        )
    ]

    # Test the run_data_tests function
    mocker.patch("faplytics_monitoring.core.factory.create", mock_factory)
    data_tester = DataTester(config=config, spark=pyspark.sql.SparkSession.getActiveSession())  # type: ignore
    result = data_tester.run_data_tests(dataset)
    assert result == expected_result


def test_select_runs(spark):
    # Test selecting runs
    HOUR = 1
    dataset_config = [{"name": "1_4", "run_at": [1, 4]}, {"name": "3", "run_at": [3]}, {"name": "empty"}]
    DataTester.timestamp_now = lambda: datetime.now().replace(hour=HOUR)
    data_tester = DataTester(config=dataset_config, spark=pyspark.sql.SparkSession.getActiveSession())  # type: ignore
    selected_runs = data_tester.select_runs(dataset_config)
    selected_runs = set(run["name"] for run in selected_runs)
    assert selected_runs == set(["1_4", "empty"])

    HOUR = 19
    dataset_config = [{"name": "4_6", "run_at": [4, 6]}, {"name": "5", "run_at": [5]}]
    DataTester.timestamp_now = lambda: datetime.now().replace(hour=HOUR)
    data_tester2 = DataTester(config=dataset_config, spark=pyspark.sql.SparkSession.getActiveSession())  # type: ignore
    selected_runs = data_tester2.select_runs(dataset_config)
    selected_runs = [run["name"] for run in selected_runs]
    assert selected_runs == ["5"]
