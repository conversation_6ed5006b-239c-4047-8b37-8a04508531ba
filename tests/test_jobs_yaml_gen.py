import json
import os
import tempfile
from pathlib import Path

from faplytics_monitoring.generate_jobs_yaml import create_jobs_config, read_datasets


def test_read_datasets(template_testing_config):

    with tempfile.NamedTemporaryFile("w", delete=False) as tmp:
        tmp.write(json.dumps(template_testing_config))
        tmp.seek(0)
        datasets = read_datasets(Path(tmp.name))

    os.unlink(tmp.name)
    assert datasets == ["fap4_events"]


def test_read_datasets_empty():
    content = """
    wrong_key:
        key1: value1
        key2: value2
    """

    with tempfile.NamedTemporaryFile("w", delete=False) as tmp:
        tmp.write(content)
        tmp.seek(0)
        datasets = read_datasets(Path(tmp.name))

    os.unlink(tmp.name)

    assert datasets == []


def test_create_jobs_config(template_testing_config, capsys):
    stage_prefix = "stagename"
    with tempfile.NamedTemporaryFile("w", delete=False, prefix=stage_prefix) as tmp:
        tmp.write(json.dumps(template_testing_config))
        tmp.seek(0)
        jobs_config = create_jobs_config([Path(tmp.name)])

        stage_name = next(iter(jobs_config))
    assert isinstance(jobs_config, dict)
    assert stage_name.startswith(stage_prefix)
    assert len(jobs_config[stage_name]["jobs"]) == 1
    assert {
        "clusters",
        "permissions",
        "schedule",
        "email_notifications",
        "name",
        "id",
        "always_running",
        "tasks",
    } == set(jobs_config[stage_name]["jobs"][0].keys())
    assert len(jobs_config[stage_name]["jobs"][0]["tasks"]) == 3
