import logging
import os
import sys

import click

import faplytics_monitoring.main as main


@click.group()
@click.option("--testing/--no-testing", type=bool, default=False)
@click.pass_context
def cli(ctx, testing: bool):
    logging.basicConfig(
        level=logging.WARNING,
        format="%(asctime)s %(name)-30s %(levelname)-8s :: %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
        stream=sys.stdout,
    )
    logging.getLogger("faplytics").setLevel(logging.INFO)
    logging.getLogger("faplytics_monitoring").setLevel(logging.INFO)
    # fixed messed up faplytics handler leading to duplicate logs
    logging.getLogger("faplytics").handlers.clear()

    if testing:
        os.environ["WEBHOOK_URL"] = "print"
    ctx.obj = main.DataTester(testing)


@cli.command()
@click.option("--dataset", default=None)
@click.option("--show-result/--no-show-result", default=False)
@click.option("--debug/--no-debug", default=False)
@click.pass_obj
def run(tester: main.DataTester, dataset: str, show_result: bool, debug: bool):
    if debug:
        [click.echo(f"{k}: {v}") for k, v in os.environ.items()]

    tester.test_runner(dataset=dataset, show_result=show_result)


@cli.command()
@click.option("--on-run", multiple=True, type=click.INT, help="send digest on these runs of the day", default=(1,))
@click.pass_obj
def digest(tester: main.DataTester, on_run: tuple[int]):
    tester.send_digest(on_run)


@cli.command()
@click.pass_obj
def notify(tester: main.DataTester):
    tester.send_notification()


def cli_runner(*args, **kwargs):
    cli(*args, **kwargs, standalone_mode=False)
