"""Monitors fap5 decoder errors.

Use decoder_errors.yaml to specify known issues that won't be considered to be critical.
"""
import json
import operator
from dataclasses import dataclass, field
from datetime import date
from functools import partial
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import pandas as pd
import yaml
from faplytics.pipelines import Dataset
from pyspark.sql import DataFrame
from pyspark.sql import functions as F

from faplytics_monitoring.core import factory
from faplytics_monitoring.core.datatest import FaplyticsDataTest
from faplytics_monitoring.core.errors import ResultStatus


@dataclass
class KnownIssue:
    name: str  # Name of the issue.
    error_msg: str  # Expected decoding_exception message.
    ihd_versions: Optional[List[int]] = None  # If None, all versions are affected.
    ticket: str = ""  # Link to ticket.


@dataclass
class EmptyMsgThreshold:
    name: str  # Name of the issue.
    abs_tol: int  # Critical -> Warning if number of empty msgs is below given threshold.
    rel_tol: float  # Critical -> Warning if rate of empty msgs is below given threshold.
    ihd_versions: Optional[List[int]] = None  # If None, all versions are affected.
    ticket: str = ""  # Link to ticket.


@dataclass
class DecoderErrorsConfig:
    result_col: str  # Name of the decoded data column
    known_issues: List[KnownIssue] = field(default_factory=list)
    empty_msg_thresholds: List[EmptyMsgThreshold] = field(default_factory=list)
    unknown_errors_threshold: int = 0

    @classmethod
    def from_dict(cls, config_dict):
        result_col = config_dict["result_col"]
        known_issues = [KnownIssue(**issue) for issue in config_dict.get("known_issues", [])]
        empty_msg_thresholds = [
            EmptyMsgThreshold(**threshold) for threshold in config_dict.get("empty_msg_thesholds", [])
        ]
        unknown_errors_threshold = config_dict.get("unknown_errors_threshold", 0)
        return cls(result_col, known_issues, empty_msg_thresholds, unknown_errors_threshold)


@dataclass
class DecoderErrors(FaplyticsDataTest):
    dataset: str

    def __post_init__(self):
        """
        set default return values. The purpose of this is to clarify
        which variable names are reserved
        """
        self.status_code = ResultStatus.critical
        self.message = None
        self.html = None
        self.date = date.today()

    def run(self, _) -> None:
        with open((Path(__file__) / ".." / "decoder_errors.yaml").resolve(), "r") as fin:
            configs = yaml.safe_load(fin)
        config = DecoderErrorsConfig.from_dict(configs[self.dataset])

        df = Dataset.for_name(self.dataset).read()
        df = df.filter(F.col("received_date") == self.date)

        health_counts = DecoderErrors.get_health_counts(df, config)
        self.status_code, self.message, self.html = DecoderErrors.get_check_results(
            health_counts, config.unknown_errors_threshold
        )

    @staticmethod
    def get_health_counts(msgs: DataFrame, config: DecoderErrorsConfig) -> pd.DataFrame:
        """Return DataFrame counting R4R messages of different error categories.

        Args:
            msgs: fap5_incidents_decoder or fap5_histogram_decoded DataFrame
            config: Specifies data column and known issues
        """

        def get_empty_msg_info(row) -> Optional[Tuple[str, str]]:
            if row.category != "empty":
                return None
            for threshold in config.empty_msg_thresholds:
                if threshold.ihd_versions and row.ihd_version not in threshold.ihd_versions:
                    continue
                if row["count"] < threshold.abs_tol or row.share < threshold.rel_tol:
                    return "known", threshold.name
            return "unknown", "empty"

        tmp = DecoderErrors.add_health_info(msgs, config.result_col, config.known_issues)
        pdf = tmp.groupby("ihd_version", "category", "errors").count().toPandas()
        pdf["share"] = pdf["count"] / pdf.groupby("ihd_version")["count"].transform(sum)
        pdf["_tmp"] = pdf.apply(get_empty_msg_info, axis=1)
        pdf["category"] = pdf.apply(lambda row: row._tmp[0] if row._tmp else row.category, axis=1)
        pdf["errors"] = pdf.apply(lambda row: [row._tmp[1]] if row._tmp else row.errors, axis=1)
        pdf = pdf.drop(columns=["_tmp"])
        return pdf

    @staticmethod
    def add_health_info(msgs: DataFrame, result_col: str, known_issues: Optional[List[KnownIssue]] = None) -> DataFrame:
        """Append columns for health category and error name to each R4R incident msg row.

        Health categories:
        * ok
        * known
        * unknown
        * empty

        Error names:
        * "" if health category is ok
        * Name of the KnownError if health category is known_error
        * Concatenation of set of error_messages if category is unknown_error
        * "" if R4R message is empty.

        Args:
            msgs: fap5_incidents_decoded DataFrame
            known_issues: List of known issues
            ihds_with_empty_msgs: List of ihd versions for which empty R4R messages are expected
            result_col: incidents for incident DataFrame histogram for Histogram DataFrame

        Returns:
            Input DataFrame with category and error columns.
        """
        known_issues = known_issues or []

        # Initialize when chains with dummy when expression that never evaluates to True
        category_map = F.when(F.lit(False), None)
        error_map = F.when(F.lit(False), None)
        for known_issue in known_issues:
            issue_filter = F.array_max(
                F.transform(F.col("decoding_exceptions").message, partial(operator.eq, known_issue.error_msg))
            )
            if known_issue.ihd_versions:
                issue_filter = issue_filter & F.col("ihd_version").isin(known_issue.ihd_versions)
            category_map = category_map.when(issue_filter, "known")
            error_map = error_map.when(issue_filter, F.array(F.lit(known_issue.name)))

        category_map = category_map.when(F.size("decoding_exceptions") > 0, "unknown")
        error_map = error_map.when(
            F.size("decoding_exceptions") > 0, F.array_distinct(F.col("decoding_exceptions").message)
        )
        category_map = category_map.when(F.size(result_col) == 0, "empty").otherwise("ok")

        msgs = msgs.withColumn("category", category_map)
        msgs = msgs.withColumn("errors", error_map)
        return msgs

    @staticmethod
    def get_check_results(
        health_counts: pd.DataFrame, unknown_error_threshold: int
    ) -> Tuple[ResultStatus, Optional[Dict[str, Any]], Optional[str]]:
        """Return check results from health_counts.

        Args:
            health_counts: DataFrame generated from `.get_health_counts`.
            unknown_error_threshold: If number of unknown errors exceeds threshold, errors are considered to be critical.

        Returns:
            Tuple of ResultStatus, message, and html string.
        """
        unknown = health_counts.query("category == 'unknown'")
        known = health_counts.query("category == 'known'")
        num_unknown_errors = unknown["count"].sum().tolist()
        num_known_errors = known["count"].sum().tolist()

        error_info = {}
        if num_unknown_errors:
            error_info["unknown_errors_count"] = json.dumps(num_unknown_errors)
            error_info["unknown_errors_affected_ihd_versions"] = json.dumps(
                sorted(unknown["ihd_version"].unique().tolist())
            )
            error_info["unknown_errors_msgs"] = json.dumps(
                sorted(unknown["errors"].transform(set).agg(lambda x: set.union(*x)))
            )
        if num_known_errors:
            error_info["known_errors_count"] = json.dumps(num_known_errors)
            error_info["known_errors_affected_ihd_versions"] = json.dumps(
                sorted(known["ihd_version"].unique().tolist())
            )
            error_info["known_errors_msgs"] = json.dumps(
                sorted(known["errors"].transform(set).agg(lambda x: set.union(*x)))
            )

        if num_unknown_errors > unknown_error_threshold:
            return ResultStatus.critical, error_info, None
        if num_known_errors > 0 or num_unknown_errors > 0:
            return ResultStatus.warning, error_info, None
        return ResultStatus.success, None, None


def register() -> None:
    factory.register("decoder_errors", DecoderErrors)
