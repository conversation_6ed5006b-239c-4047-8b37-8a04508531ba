import logging
from dataclasses import dataclass, field
from datetime import datetime, timedelta, timezone
from pathlib import Path
from typing import List, Optional, Union

import pyspark.sql.functions as F
from faplytics.pipelines import Dataset
from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.types import DoubleType, LongType

from faplytics_monitoring.core import factory
from faplytics_monitoring.core.datatest import FaplyticsDataTest
from faplytics_monitoring.core.errors import ResultStatus

log = logging.getLogger(__name__)


@dataclass
class DataCount(FaplyticsDataTest):

    dataset: str
    time_column: str or list
    monitoring_dataset_path: Union[str, Path]
    dataset_path: Optional[Union[str, Path]] = None
    time_column_factor: int = 1
    date_format: str = "%Y-%m-%d"
    duration_hours: float = 24.0
    # Outlierdetection 1.5 ~ 2.7 sigma tolerance, 2 ~ 3.2 sigma tolerance
    iqr_range_days: int = 14
    iqr_multiplier: float = 3
    sql: str = ""
    # List of columns requiring duplicate removal
    drop_duplicates: List[str] = field(default_factory=list)
    alert_threshold: int = None
    count_var: str = "data_count"

    def __post_init__(self):
        """
        set default return values. The purpose of this is to clarify
        which variable names are reserved
        """
        self.status_code = ResultStatus.critical
        self.message = None
        self.html = None

        self.start_datetime = datetime.now(timezone.utc) - timedelta(hours=self.duration_hours)

        # if the time column has a unix timestamp format, it needs to be converted (this is requiered p.e for vvr data)

        if self.time_column == "year,month,day":
            raise ValueError("year,month,day is not supported for this test.")

    def _pre_filter(self, df: DataFrame) -> DataFrame:
        # Pre-filter on partition column for speed-up
        if {"year", "month", "day"}.issubset(set(df.columns)):
            log.info("Using pre-filter on 'year', 'month', 'day' columns for speed up.")

            filter_condition = (
                f"(year == {self.start_datetime.year} AND month == {self.start_datetime.month} AND day >= {self.start_datetime.day}) OR "  # type: ignore
                f"(year == {self.start_datetime.year} AND month > {self.start_datetime.month}) OR "  # type: ignore
                f"(year > {self.start_datetime.year})"  # type: ignore
            )
            df = df.filter(filter_condition)

        elif "date" in df.columns:
            log.info(f"Using pre-filter on 'date' column with format '{self.date_format}' for speed up.")
            df = df.filter(F.col("date") >= self.start_datetime.date().strftime(self.date_format))

        return df

    def _get_data_count(self, spark: SparkSession) -> int:
        if self.dataset_path:
            df = spark.read.format("delta").load(str(self.dataset_path))
        else:
            df = Dataset.for_name(self.dataset).read()

        # Get type of time column
        if df.schema[self.time_column].dataType in [LongType(), DoubleType()]:
            filter_value = datetime.timestamp(self.start_datetime) * self.time_column_factor
        else:
            filter_value = self.start_datetime

        df = self._pre_filter(df)
        df = df.filter(F.col(self.time_column) >= filter_value)
        if self.sql:
            df.createOrReplaceTempView("df_table")
            df = spark.sql(self.sql)
        if self.drop_duplicates:
            df = df.drop_duplicates(self.drop_duplicates)
        return df.count()

    def _retrieve_prev_results(
        self, *, last_n_days: int = 28, max_result_code: int = 3, spark: SparkSession
    ) -> DataFrame:
        return (
            spark.read.format("delta")
            .load(str(self.monitoring_dataset_path))
            .filter(F.col("time") > (F.current_timestamp() - F.expr(f"INTERVAL {last_n_days} DAYS")))
            .filter(F.col("test_type") == "data_count")
            .filter(F.col("dataset") == self.dataset)
            .filter(F.col("test_result_code") <= max_result_code)
        )

    def _calc_IQR(
        self, results_table: DataFrame, *, count_column: str = "test_result_map.data_count"
    ) -> tuple[int, int]:
        data = results_table.selectExpr(f"int({count_column}) as count")

        f_row = data.groupBy().agg(F.stddev("count").alias("std"), F.avg("count").alias("avg")).collect()[0]
        f_delta = f_row.std * self.iqr_multiplier
        return max(0, round(f_row.avg - f_delta)), max(0, round(f_row.avg + f_delta))

    def _handle_previous_results(self, spark: SparkSession) -> Union[str, dict]:
        try:
            # Get the total daily of the last run
            results_table = self._retrieve_prev_results(spark=spark, last_n_days=self.iqr_range_days)

            info = dict()
            # Get de Bounds for outliers
            info["min"], info["max"] = self._calc_IQR(results_table, count_column=f"test_result_map.{self.count_var}")

        except Exception as e:
            return f"Failed to compute outliers with {e}"

        return info

    def _set_result_code(self, data_count, prev_runs_info):

        self.message = {self.count_var: str(data_count)}

        if self.alert_threshold is not None:
            if data_count >= self.alert_threshold:
                self.status_code = ResultStatus.error
            else:
                self.status_code = ResultStatus.success
            return

        # For data_count as default
        if isinstance(prev_runs_info, dict):
            if data_count > 0:
                self.status_code = ResultStatus.success
                self.message["outlier"] = f'[{prev_runs_info["min"]},{prev_runs_info["max"]}]'

            if not prev_runs_info["min"] <= data_count <= prev_runs_info["max"]:
                self.status_code = ResultStatus.error
                self.message["outlier"] = f'Datacount not within [{prev_runs_info["min"]},{prev_runs_info["max"]}]'

        elif isinstance(prev_runs_info, str):
            self.status_code = ResultStatus.error
            self.message["error"] = prev_runs_info

    def run(self, spark: SparkSession) -> None:
        data_count = self._get_data_count(spark)
        prev_runs_info = self._handle_previous_results(spark)
        self._set_result_code(data_count=data_count, prev_runs_info=prev_runs_info)


def register() -> None:
    factory.register("data_count", DataCount)
