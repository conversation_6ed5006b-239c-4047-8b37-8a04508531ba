from dataclasses import dataclass
from pathlib import Path
from typing import Optional, Union

import pyspark.sql.functions as F
from faplytics.pipelines import Dataset
from pyspark.sql import DataFrame, Row, SparkSession, utils

from faplytics_monitoring.core import factory
from faplytics_monitoring.core.datatest import FaplyticsDataTest
from faplytics_monitoring.core.errors import ResultStatus


@dataclass
class SchemaMonitor(FaplyticsDataTest):
    dataset: str
    monitoring_dataset_path: Union[str, Path]
    dataset_path: Optional[Union[str, Path]] = None

    def __post_init__(self):
        """
        set default return values. The purpose of this is to clarify
        which variable names are reserved
        """
        self.status_code = ResultStatus.critical
        self.message = dict()
        self.html = None

    def _check_schema_change(self, df, prev_schema):
        df_schema_json = df.schema.json()
        self.message["schema"] = df_schema_json
        if df_schema_json == prev_schema:
            self.status_code = ResultStatus.success
        else:
            self.message["critical"] = f"There is a change in schema for {self.dataset}."
            self.status_code = ResultStatus.critical

    def _retrieve_prev_result(self, spark: SparkSession) -> Optional[Row]:
        return (
            spark.read.format("delta")
            .load(str(self.monitoring_dataset_path))
            .filter(F.col("test_type") == "schema_monitor")
            .filter(F.col("dataset") == self.dataset)
            .orderBy(F.col("time").desc())
            .first()
        )

    def _get_dataframe(self, spark: SparkSession) -> DataFrame:
        if self.dataset_path:
            df = spark.read.format("delta").load(str(self.dataset_path))
        else:
            df = Dataset.for_name(self.dataset).read()

        return df

    def run(self, spark: SparkSession) -> None:
        df = self._get_dataframe(spark)
        try:
            prev_test_result = self._retrieve_prev_result(spark)
            if prev_test_result is not None and "schema" in prev_test_result["test_result_map"]:
                prev_schema = prev_test_result["test_result_map"]["schema"]
                self._check_schema_change(df, prev_schema)
            else:
                self.message["success"] = "Schema saved as JSON in monitoring dataset."
                self.message["schema"] = df.schema.json()
                self.status_code = ResultStatus.success

        except utils.AnalysisException as ae:
            self.status_code = ResultStatus.error
            self.message["error"] = f"Failed to execute schema check for {self.dataset}. Error:: {ae}"


def register() -> None:
    factory.register("schema_monitor", SchemaMonitor)
