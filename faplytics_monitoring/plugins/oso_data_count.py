from dataclasses import dataclass
from datetime import datetime, timedelta, timezone
from pathlib import Path
from typing import Optional, Union

import pyspark.sql.functions as F
from faplytics.pipelines import Dataset
from pyspark.sql import DataFrame, SparkSession

from faplytics_monitoring.core import factory
from faplytics_monitoring.core.datatest import FaplyticsDataTest
from faplytics_monitoring.core.errors import ResultStatus


@dataclass
class OsoDataCount(FaplyticsDataTest):
    dataset: str
    time_column: str or list
    monitoring_dataset_path: Union[str, Path]
    dataset_path: Optional[Union[str, Path]] = None
    threshold_gap_overlap: int = 80
    threshold_gps_oso: int = 80

    def __post_init__(self):
        """
        set default return values. The purpose of this is to clarify
        which variable names are reserved
        """
        self.status_code = ResultStatus.critical
        self.message = None
        self.html = None

        self.start_datetime = datetime.now(timezone.utc).replace(microsecond=0, second=0, minute=0, hour=0)

    def _get_total_daily_oso_count(self, spark: SparkSession) -> DataFrame:

        if self.dataset_path:
            df = spark.read.format("delta").load(str(self.dataset_path))
        else:
            df = Dataset.for_name(self.dataset).read()

        oso_daily_dataframe = (
            df.filter(
                (F.col("ihd_version") == 445)
                & (F.col("date") >= (self.start_datetime - timedelta(days=1)).strftime("%Y-%m-%d"))
            )
            .filter(F.col(self.time_column) >= (self.start_datetime - timedelta(days=1)))
            .filter((F.col("sub_system") == "HAF-HMHQ") & (F.col("incident_name").like("%OSO%")))
        )

        return oso_daily_dataframe

    def _set_result(self, total_oso_daily_count, incident_wise_count):
        self.message = {"Total Daily oso count ": str(total_oso_daily_count)}
        if total_oso_daily_count == 0:
            self.status_code = ResultStatus.success
            self.message["outlier"] = "The OSO related incident count is 0"
            return
        lst_oso_inc = dict(incident_wise_count.collect())
        gap_overlap_count = lst_oso_inc.get("I_FAILED_OSO_VALIDATION_GAP_OVERLAP", 0)
        gps_oso_count = lst_oso_inc.get("I_FAILED_OSO_VALIDATION_GPS", 0)
        self.status_code = ResultStatus.error
        if gps_oso_count > self.threshold_gps_oso and gap_overlap_count > self.threshold_gap_overlap:
            self.message[
                "warning"
            ] = f"GPS OSO and Gap Overlap OSO Thresholds exceeded, {gps_oso_count=} & {gap_overlap_count=}."
        elif gps_oso_count > self.threshold_gps_oso:
            self.message[
                "warning"
            ] = f"OSO Incident GPS count exceeds threshold. {gps_oso_count} > {self.threshold_gps_oso}"
        elif gap_overlap_count > self.threshold_gap_overlap:
            self.message[
                "warning"
            ] = f"OSO Incident Gap overlap exceeds threshold. {gap_overlap_count} > {self.threshold_gap_overlap}"
        else:  # no thresholds exceeded
            self.status_code = ResultStatus.success

    def run(self, spark: SparkSession) -> None:
        filtered_data = self._get_total_daily_oso_count(spark)
        total_oso_daily_count = filtered_data.count()  # Daily OSO count
        incident_wise_count = filtered_data.groupBy("incident_name").count()  # OSO Incident wise count
        self._set_result(total_oso_daily_count=total_oso_daily_count, incident_wise_count=incident_wise_count)


def register() -> None:
    factory.register("oso_data_count", OsoDataCount)
