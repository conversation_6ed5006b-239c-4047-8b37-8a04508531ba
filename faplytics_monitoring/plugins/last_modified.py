import logging
import os
from dataclasses import dataclass
from datetime import datetime, timezone
from typing import Optional, Tuple, Union

import dateparser
import pyspark.sql.functions as F
from delta.tables import DeltaTable
from faplytics.pipelines import Dataset
from pyspark.sql import SparkSession, utils

from faplytics_monitoring.core import factory
from faplytics_monitoring.core.datatest import FaplyticsDataTest
from faplytics_monitoring.core.errors import ResultStatus

log = logging.getLogger(__name__)


@dataclass
class LastModified(FaplyticsDataTest):
    """Dataset needs to be a delta table for this !"""

    dataset: str
    dataset_path: Optional[str] = None
    time_column: Optional[str] = None
    success_threshold_mins: int = 120
    warning_threshold_mins: int = 180
    error_threshold_mins: int = 240

    def __post_init__(self):
        """
        set default return values. The purpose of this is to clarify
        which variable names are reserved
        """
        self.status_code = ResultStatus.critical
        self.message = dict()
        self.html = None

        if not self.dataset_path:
            self.dataset_path = Dataset.for_name(self.dataset).data_path

        if not self.success_threshold_mins < self.warning_threshold_mins < self.error_threshold_mins:
            raise ValueError("Last modified thresholds must be increasing.")

    def run(self, spark: SparkSession) -> None:

        latest_version, time_of_last_update = self.get_time_of_last_update(spark)
        minutes_to_last_update = (datetime.now(timezone.utc) - time_of_last_update).total_seconds() // 60
        self.message["time_of_last_delta_update"] = str(time_of_last_update)  # type: ignore
        self.message["minutes_to_last_update"] = str(minutes_to_last_update)  # type: ignore

        if self.time_column:
            try:
                added_parquets = self.get_parquets_of_last_commit(spark, latest_version)
                time_of_latest_datapoint, time_of_earliest_datapoint = self.get_latest_datapoint(added_parquets, spark)

                if time_of_latest_datapoint:
                    minutes_to_last_update = (datetime.now(timezone.utc) - time_of_latest_datapoint).total_seconds() // 60
                    self.message["time_of_latest_datapoint"] = str(time_of_latest_datapoint)  # type: ignore
                    self.message["minutes_to_latest_datapoint"] = str(minutes_to_last_update)  # type: ignore

                if time_of_earliest_datapoint:
                    minutes_to_earliest_update = (
                        datetime.now(timezone.utc) - time_of_earliest_datapoint
                    ).total_seconds() // 60
                    self.message["time_of_earliest_datapoint"] = str(time_of_earliest_datapoint)  # type: ignore
                    self.message["minutes_to_earliest_datapoint"] = str(minutes_to_earliest_update)  # type: ignore
            except Exception as e:
                log.error(f"Error processing time column data for {self.dataset}: {e}")
                self.message["time_column_error"] = str(e)  # type: ignore

        if minutes_to_last_update < self.success_threshold_mins:
            self.status_code = ResultStatus.success
        elif minutes_to_last_update < self.warning_threshold_mins:
            self.status_code = ResultStatus.warning
        elif minutes_to_last_update < self.error_threshold_mins:
            self.status_code = ResultStatus.error

    def get_time_of_last_update(self, spark: SparkSession) -> tuple[str, datetime]:
        """
        retrieves the timestamp of the last update to the deltatable
        """

        add_operations = ["WRITE", "UPDATE", "STREAMING UPDATE"]

        last_mod = (
            DeltaTable.forPath(spark, self.dataset_path)  # type: ignore
            .history(99)
            .filter(F.col("operation").isin(add_operations))
            .agg({"timestamp": "max", "version": "max"})
        )
        [latest_version, date_of_last_mod] = last_mod.collect()[0]

        return latest_version, date_of_last_mod.replace(tzinfo=timezone.utc)

    def get_parquets_of_last_commit(self, spark: SparkSession, latest_version: str) -> list[str]:
        delta_log_path = path_join(self.dataset_path, "_delta_log", f"{latest_version:020d}.json")  # type: ignore
        try:
            # Read the Delta log JSON file
            log_df = spark.read.format("json").load(str(delta_log_path))

            # Check if 'add' column exists in the schema
            if 'add' not in log_df.columns:
                log.warning(f"No 'add' column found in Delta log for {self.dataset_path}. Available columns: {log_df.columns}")
                return []

            # Filter for add operations and extract paths
            commit_data = (
                log_df
                .filter(F.col("add").isNotNull())
                .select("add.path")
                .filter(F.col("path").isNotNull())
                .collect()
            )

            return [path_join(self.dataset_path, cur_commit["path"]) for cur_commit in commit_data]  # type: ignore
        except (FileNotFoundError, utils.AnalysisException) as e:
            log.warning(f"Could not read Delta log for {self.dataset_path}: {e}")
            return []

    def get_latest_datapoint(self, file_paths: list[str], spark: SparkSession) -> Tuple[Optional[datetime], Optional[datetime]]:
        # if a parquet from the file paths is not found, we read the entire dataframe
        try:
            if not file_paths:
                log.info(f"No parquet files found, loading all data for {self.dataset_path}")
                df = spark.read.format("delta").load(self.dataset_path)
                max_time = df.select(F.max(self.time_column)).head()[0]  # type: ignore
                earliest_time = None
            else:
                log.info(f"Loading the last {len(file_paths)} parquet files of {self.dataset_path}")
                df = spark.read.format("parquet").load(file_paths)
                max_time, min_time = df.select(F.max(self.time_column), F.min(self.time_column)).head()
                earliest_time = dateparser.parse(str(min_time)).replace(tzinfo=timezone.utc) if min_time else None

        except utils.AnalysisException as e:
            log.warning(f"Error loading parquet files, falling back to loading all data for {self.dataset_path}: {e}")
            try:
                df = spark.read.format("delta").load(self.dataset_path)
                max_time = df.select(F.max(self.time_column)).head()[0]  # type: ignore
                earliest_time = None
            except Exception as fallback_error:
                log.error(f"Failed to load data from {self.dataset_path}: {fallback_error}")
                raise

        if max_time is None:
            log.warning(f"No data found in time column {self.time_column} for {self.dataset_path}")
            return None, None

        latest_time = dateparser.parse(str(max_time))
        if latest_time is None:
            log.error(f"Could not parse max_time: {max_time}")
            raise ValueError(f"Could not parse timestamp: {max_time}")

        return latest_time.replace(tzinfo=timezone.utc), earliest_time


def path_join(first: str, *parts: str) -> str:
    if "://" in first:
        # it's an url
        return first.rstrip("/") + "/" + "/".join(p.strip("/") for p in parts)
    else:
        return os.path.join(first, *parts)


def register() -> None:
    factory.register("last_modified", LastModified)
