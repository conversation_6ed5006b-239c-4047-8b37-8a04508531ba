import json
from dataclasses import dataclass, field
from datetime import date
from pathlib import Path
from typing import List, Optional

import yaml
from faplytics.pipelines import Dataset
from pyspark.sql import functions as F

from faplytics_monitoring.core import factory
from faplytics_monitoring.core.datatest import FaplyticsDataTest
from faplytics_monitoring.core.errors import ResultStatus


class KnownIssue:
    def __init__(
        self,
        name: str,
        ticket: Optional[str] = None,
        end_date: Optional[date] = None,
        start_date: Optional[date] = None,
    ):
        self.name = name
        self.ticket = ticket
        self.end_date = date.fromisoformat(end_date) if end_date else None
        self.start_date = date.fromisoformat(start_date) if start_date else None


@dataclass
class SessionHashCheckConfig:
    known_issues: List[KnownIssue] = field(default_factory=list)

    @classmethod
    def from_dict(cls, config_dict):
        known_issues = [KnownIssue(**issue) for issue in config_dict.get("known_issues", [])]
        return cls(known_issues)


def get_config() -> SessionHashCheckConfig:
    with open((Path(__file__) / ".." / "session_hash_check.yaml").resolve(), "r") as fin:
        configs = yaml.safe_load(fin)
    return SessionHashCheckConfig.from_dict(configs)


CONFIG = get_config()


@dataclass
class SessionHashCheck(FaplyticsDataTest):
    dataset: str

    def __post_init__(self):
        """
        set default return values. The purpose of this is to clarify
        which variable names are reserved
        """
        self.status_code = ResultStatus.critical
        self.message = None
        self.html = None
        self.date = date.today()

    def run(self, _) -> None:
        df = Dataset.for_name(self.dataset).read()
        df = df.filter(F.col("received_date") == self.date)
        df = add_session_hash_cols(df)
        self.status_code, self.message, self.html = get_check_results(df, self.date, CONFIG.known_issues)


def key_vals_list(key_array, val_array, key_name="key", vals_name="vals"):
    key_list = F.filter(F.array_distinct(key_array), lambda x: x.isNotNull()).alias(key_name)
    vals_list = F.transform(key_list, lambda _: F.filter(val_array, lambda x: F.lit(False))).alias(vals_name)
    key_vals_list = F.arrays_zip(key_list, vals_list)
    return F.aggregate(
        F.arrays_zip(key_array.alias("key"), val_array.alias("val")),
        key_vals_list,
        lambda kv_list, k_v: (
            F.transform(
                kv_list,
                lambda key_vals: (
                    F.when(
                        (key_vals[key_name] == k_v.key) & (~F.array_contains(key_vals[vals_name], k_v.val)),
                        F.struct(key_vals[key_name], F.array_append(key_vals[vals_name], k_v.val).alias(vals_name)),
                    ).otherwise(key_vals)
                ),
            )
        ),
    )


def add_session_hash_cols(df):
    tmp = df
    tmp = tmp.withColumn(
        "session_hashes", key_vals_list(F.col("incidents.session"), F.col("sessionHashList"), "session", "hashes")
    )
    tmp = tmp.withColumn(
        "hash_sessions", key_vals_list(F.col("sessionHashList"), F.col("incidents.session"), "hash", "sessions")
    )
    tmp = tmp.withColumn(
        "max_hashes_per_session", F.array_max(F.transform("session_hashes", lambda x: F.size(x.hashes)))
    )
    tmp = tmp.withColumn(
        "max_sessions_per_hash", F.array_max(F.transform("hash_sessions", lambda x: F.size(x.sessions)))
    )
    tmp = tmp.withColumn("multi_hashes", (F.col("max_hashes_per_session") > 1).cast("int"))
    tmp = tmp.withColumn("multi_sessions", (F.col("max_sessions_per_hash") > 1).cast("int"))
    return tmp


def get_check_results(df, date: date, known_issues: List[KnownIssue]):
    r4r, multi_hashes, multiple_sessions = (
        df.groupby().agg(F.count("*").alias("num_r4r"), F.sum("multi_hashes"), F.sum("multi_sessions")).collect()[0]
    )
    multi_hashes = multi_hashes or 0
    multiple_sessions = multiple_sessions or 0
    error_msgs = []
    if multi_hashes > 0:
        error_msgs.append(f"Found {multi_hashes} out of {r4r} R4R messages with different hashes for same session.")
    if multiple_sessions > 0:
        error_msgs.append(f"Found {multiple_sessions} out of {r4r} R4R messages with different sessions for same hash.")
    if not error_msgs:
        return ResultStatus.success, None, None

    state = ResultStatus.critical
    for known_issue in known_issues:
        if known_issue.start_date and date < known_issue.start_date:
            continue
        if known_issue.end_date and date > known_issue.end_date:
            continue
        error_msgs.append(f"Known issue: {known_issue.name}.")
        state = ResultStatus.warning
    message = {"error_msgs": json.dumps(error_msgs)}
    return state, message, None


def register() -> None:
    factory.register("session_hash_check", SessionHashCheck)
