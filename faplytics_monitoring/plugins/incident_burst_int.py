from dataclasses import dataclass
from datetime import datetime, timedelta

import pandas as pd
import pyspark.sql.functions as F
from faplytics.pipelines import Dataset
from pyspark.sql import DataFrame, Window

from faplytics_monitoring.core import factory
from faplytics_monitoring.core.datatest import FaplyticsDataTest
from faplytics_monitoring.core.errors import ResultStatus

INCIDENT_BURST_THRESHOLD = 1000
NUM_INCIDENTS_COL = "#incidents"
NUM_INCIDENTS_IN_BIGGEST_BURST_COL = "#incidents_in_biggest_burst"


@dataclass
class IncidentBurstInt(FaplyticsDataTest):
    dataset: str
    max_vins: int = 100

    def __post_init__(self):
        """
        set default return values. The purpose of this is to clarify
        which variable names are reserved
        """
        self.status_code = ResultStatus.critical
        self.message = None
        self.html = None

        self.start_datetime = datetime.utcnow().replace(microsecond=0, second=0, minute=0, hour=0)
        self.end_datetime = self.start_datetime + timedelta(hours=24)

    def run(self, _) -> None:

        df = Dataset.for_name(self.dataset).read()
        df = df.filter(F.col("incident_time").between(self.start_datetime, self.end_datetime))
        incident_bursts = self.get_incident_bursts(df)

        num_bursts = incident_bursts.count()

        if num_bursts == 0:
            self.status_code = ResultStatus.success
            self.message = {
                "incident_burst_threshold": str(INCIDENT_BURST_THRESHOLD),
                "num_bursts": "0",
            }
            return

        vins = [row.vin for row in incident_bursts.select("vin").distinct().collect()]
        ihd_versions = [row.ihd_version for row in incident_bursts.select("ihd_version").distinct().collect()]
        top10 = incident_bursts.orderBy(F.col(NUM_INCIDENTS_COL).desc()).limit(10).toPandas()
        top_per_vin = self.get_biggest_burst_per_col_value(incident_bursts, column="vin")
        top_per_incident_name = self.get_biggest_burst_per_col_value(incident_bursts, column="incident_name")

        self.message = {
            "incident_burst_threshold": str(INCIDENT_BURST_THRESHOLD),
            "num_bursts": str(num_bursts),
            "#incidents_in_biggest_bursts": str(top10[NUM_INCIDENTS_COL][0]),
            "affected_vins": str(vins),
            "affected_ihd_versions": str(ihd_versions),
        }
        self.html = self._get_html(top10, top_per_vin, top_per_incident_name)
        self.status_code = ResultStatus.warning if len(vins) < self.max_vins else ResultStatus.error

    def _get_html(self, top10: pd.DataFrame, top_per_vin: pd.DataFrame, top_per_incident_name: pd.DataFrame):
        return f"""\
<!DOCTYPE html>
<html>
<head>
<title>Incident bursts from {self.start_datetime} to {self.end_datetime} </title>
</head>
<body>

<h1>Top 10 biggest bursts</h1>
{top10.to_html()}

<h1>Top 10 vehicles with biggest bursts</h1>
{top_per_vin.head(10).to_html()}

<h1>Top 10 incident names with biggest bursts</h1>
{top_per_incident_name.head(10).to_html()}

</body>
</html>
"""

    @staticmethod
    def get_biggest_burst_per_col_value(incident_bursts: DataFrame, column="vin") -> pd.DataFrame:
        """Return info about biggest incident burst for per category.

        Order by number on incidents in burst in descending order.

        Args:
            incident_bursts: Incident burst DataFrame
            column: Name of column to group by.

        Returns:
            DataFrame storing info for biggest burst for each vin.
        """
        win = (
            Window.partitionBy(column)
            .orderBy(NUM_INCIDENTS_COL)
            .rangeBetween(Window.unboundedPreceding, Window.unboundedFollowing)
        )
        tmp = incident_bursts
        old_cols = [col for col in ["vin", "incident_name", "incident_time", "ihd_version"] if col != column]
        new_cols = tuple([f"{col}_of_biggest_burst" for col in old_cols])
        for old_col, new_col in zip(old_cols, new_cols):
            tmp = tmp.withColumn(new_col, F.last(old_col).over(win))
        tmp = tmp.withColumn(NUM_INCIDENTS_IN_BIGGEST_BURST_COL, F.last(NUM_INCIDENTS_COL).over(win))
        return (
            tmp.groupby(column, *new_cols, NUM_INCIDENTS_IN_BIGGEST_BURST_COL)
            .agg(F.count("*").alias("#bursts"))
            .toPandas()
            .sort_values(by=NUM_INCIDENTS_IN_BIGGEST_BURST_COL, ascending=False)
            .reset_index(drop=True)
        )

    @staticmethod
    def get_incident_bursts(df: DataFrame) -> DataFrame:
        """Return incident bursts DataFrame

        Incident burst refers to the sitation when the number of incidents of the same type
        send in an quarter hour from a single vin exceeds a threshold.

        The DataFrame stores one row for each such incident burst.

        Args:
            df: FAP5 Incident DataFrame

        Returns:
            Incident burst DataFrame.
        """
        return (
            df.groupby("incident_time", "vin", "incident_name", "ihd_version")
            .agg(F.count("*").alias(NUM_INCIDENTS_COL))
            .filter(F.col(NUM_INCIDENTS_COL) > INCIDENT_BURST_THRESHOLD)
        )


def register() -> None:
    factory.register("incident_burst_int", IncidentBurstInt)
