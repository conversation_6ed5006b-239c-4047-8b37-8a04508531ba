from dataclasses import dataclass

from faplytics.pipelines import Dataset
from pyspark.sql import SparkSession

from faplytics_monitoring.core import factory
from faplytics_monitoring.core.datatest import FaplyticsDataTest
from faplytics_monitoring.core.errors import ResultStatus


@dataclass
class TemplateTest(FaplyticsDataTest):

    dataset: str
    parameter_line_count: int

    def __post_init__(self):
        """
        set default return values. The purpose of this is to clarify
        which variable names are reserved
        """
        self.status_code = ResultStatus.critical
        self.message = None
        self.html = None

    def run(self, spark: SparkSession) -> None:
        """_summary_
        your test code goes here, self.status_code, self.message, and self.html
        should be set here as well.
        """
        df = Dataset.for_name(self.dataset).read()
        data_count = df.count()
        self.message = {"data_count": str(data_count)}
        self.status_code = ResultStatus.success


def register() -> None:
    factory.register("template_test", TemplateTest)
