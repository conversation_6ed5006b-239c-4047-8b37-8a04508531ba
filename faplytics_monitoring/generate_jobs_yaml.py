import copy
from importlib.metadata import version as package_version
from pathlib import Path
from typing import Any, Iterable

import yaml

# Job configuration
SPARK_VERSION = "14.3.x-scala2.12"
NUM_WORKERS = 2
FAPLYTICS_VERSION = package_version("faplytics")
JOB_SCHEDULE = "21 45 2/4 * * ?"


def read_datasets(yaml_file: Path) -> list[str]:
    with open(yaml_file, "r") as file:
        yaml_data = yaml.safe_load(file)
        try:
            datasets = list(yaml_data["datasets"].keys())
        except (AttributeError, KeyError):
            return []
    return datasets


def create_jobs_config(yaml_files: Iterable[Path]) -> dict[str, Any]:
    default_cluster = {
        "num_workers": NUM_WORKERS,
        "spark_version": SPARK_VERSION,
        "data_security_mode": "SINGLE_USER",
        "spark_env_vars": {
            "SKIP_GIT_SETUP": True,
            "FAPLYTICS_VERSION": FAPLYTICS_VERSION,
            "FAPLYTICS_ENV": "{env}",
            "WEBHOOK_URL": "{{{{secrets/teams-webhook/faplytics-notifications}}}}",
        },
    }

    default_permission = {"group_name": "AL_CA4ADBE.FaplyticsUser", "permission_level": "CAN_VIEW"}
    common_params = {
        "clusters": [default_cluster],
        "permissions": [default_permission],
        "jobs": {
            "monitoring_job": {
                "clusters": [default_cluster],
                "permissions": [default_permission],
                "schedule": {"quartz_cron_expression": JOB_SCHEDULE, "timezone_id": "UTC"},
                "email_notifications": {
                    "on_failure": ["<EMAIL>"],
                    "no_alert_for_skipped_runs": False,
                },
            },
            "monitoring_job_testing": {
                "clusters": [default_cluster],
                "permissions": [default_permission],
            },
        },
        "libraries": [
            {
                "pypi": {
                    "package": "faplytics_monitoring=={PROJECT_VERSION}",
                }
            },
            {
                "maven": {
                    "coordinates": f"com.daimler.ca4ad:faplytics-extensions:{package_version('faplytics-extensions')}",
                }
            },
            {
                "maven": {
                    "coordinates": f"com.daimler.ca4ad:faplytics-fap5:{package_version('faplytics-fap5')}",
                }
            },
        ],
    }

    output_data = {}

    for yaml_file in yaml_files:

        stage = yaml_file.stem
        output_data[stage] = {"databricks_host": stage.split("_")[0]}
        dataset_names = read_datasets(yaml_file)

        if "testing" not in stage:
            job_template = "monitoring_job"
            options_l, options_r = [], []
            run = False
        else:
            job_template = "monitoring_job_testing"
            options_l, options_r = ["--testing"], ["--debug"]
            run = True

        job = common_params["jobs"][job_template]
        job_name = ["faplytics", "monitoring"] + stage.split("_")
        job["name"] = " ".join(job_name)
        job["id"] = "-".join(job_name)
        job["always_running"] = run
        job["tasks"] = []
        for dataset_name in dataset_names:
            task = {
                "name": f"test-{dataset_name}",
                "libraries": common_params["libraries"],
                "python_wheel_task": {
                    "package_name": "faplytics_monitoring",
                    "entry_point": "faplytics-monitoring",
                    "parameters": options_l + ["run", "--show-result", f"--dataset={dataset_name}"] + options_r,
                },
            }
            job["tasks"].append(task)

        notify_task = {
            "name": "-".join(job_name + ["notify"]),
            "libraries": common_params["libraries"],
            "depends_ons": [{"task_key": f"test-{dataset_name}"} for dataset_name in dataset_names],
            "python_wheel_task": {
                "package_name": "faplytics_monitoring",
                "entry_point": "faplytics-monitoring",
                "parameters": options_l + ["notify"],
            },
        }
        job["tasks"].append(notify_task)

        digest_task = {
            "name": "-".join(job_name + ["digest"]),
            "libraries": common_params["libraries"],
            "depends_ons": [{"task_key": "-".join(job_name + ["notify"])}],
            "python_wheel_task": {
                "package_name": "faplytics_monitoring",
                "entry_point": "faplytics-monitoring",
                "parameters": options_l + ["digest", "--on-run", "2"],
            },
        }
        job["tasks"].append(digest_task)
        output_data[stage] = {"jobs": [copy.deepcopy(job)]}

    return output_data


if __name__ == "__main__":
    # Read in all config files
    yaml_files = (Path(__file__).parent / "configs").glob("*.yaml")
    with open("./jobs.yaml", "w") as file:
        yaml.dump(create_jobs_config(yaml_files), file)
