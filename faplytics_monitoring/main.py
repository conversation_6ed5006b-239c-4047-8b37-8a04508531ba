import datetime
import logging
import os
from pathlib import Path
from typing import Optional

import yaml
from faplytics.common.spark import get_spark_session
from faplytics.core import settings as faplytics_settings
from faplytics.plugins import discover_plugins
from pyspark.sql import SparkSession
from pyspark.sql import functions as F

from faplytics_monitoring.core import factory, loader
from faplytics_monitoring.core.dataset_utils import (
    ResultModel,
    show_monitoring_table,
    write_to_monitoring_table,
)
from faplytics_monitoring.core.errors import ResultStatus
from faplytics_monitoring.core.notification import Notifier

RUN_EVERY_X_HRS = 4

log = logging.getLogger(__name__)


class DataTester:
    def __init__(self, testing: bool = False, config: Optional[dict] = None, spark: Optional[SparkSession] = None):

        self.testing: bool = testing
        self.config: dict = config if config else self._load_config()
        self.spark: SparkSession = spark if spark else get_spark_session()

    @staticmethod
    def load_testing_config(config_file_name: str, root_path: Path) -> dict:
        print(f"Read config from {config_file_name}")
        config_file = root_path / "configs" / config_file_name
        if not config_file.is_file():
            raise FileNotFoundError(f"no config file found in {config_file!r}")

        log.info(f"Reading config from: {config_file}")
        with open(config_file) as file:
            config = yaml.safe_load(file)

        assert (
            "datasets" in config and "plugins" in config
        ), f"Config '{config_file_name}' needs keys 'plugins' and 'datasets'"

        return config

    def _load_config(self) -> dict:
        env = os.getenv("FAPLYTICS_ENV", "prod").partition("_")[0]

        discover_plugins()

        log_file = f"/Volumes/westeurope_crowd_analytics/faplytics_{env}/faplytics/settings/settings.{env}.yaml"
        log.info(f"Loading config from {log_file}")
        faplytics_settings.load_file(log_file)

        config = DataTester.load_testing_config(
            config_file_name=f"{env}{'_testing' if self.testing else ''}.yaml",
            root_path=Path(__file__).parent.resolve(),
        )
        config["env"] = env
        return config

    @staticmethod
    def timestamp_now() -> datetime.datetime:
        return datetime.datetime.now()

    @classmethod
    def _do_run_at_current_time(cls, runs: list):
        nth_run = cls.timestamp_now().hour // RUN_EVERY_X_HRS + 1
        return nth_run in runs

    @classmethod
    def select_runs(cls, test_configs: list) -> list:
        return [
            config for config in test_configs if cls._do_run_at_current_time(config.pop("run_at", [1, 2, 3, 4, 5, 6]))
        ]

    def run_data_tests(self, dataset: str) -> list[ResultModel]:
        test_result_list: list[ResultModel] = list()

        test_configs: list = self.config["datasets"][dataset]
        test_configs: list = self.select_runs(test_configs)

        for item in test_configs:
            test = factory.create(item, dataset)
            test_start = DataTester.timestamp_now()

            log.info(f"Now running {item['type']} on {dataset} with parameters: {item.get('params', {})}")
            try:
                test.run(self.spark)
                test_result_map = test.message
            except Exception as e:
                test_result_map = {"run failed": str(e)}
                print(e)

            test_result: ResultModel = ResultModel(
                dataset=dataset,
                test_type=item["type"],
                test_name=item["name"],
                test_group=item["group"],
                test_result_code=test.status_code.value,
                test_result_map=test_result_map,
                test_start=test_start,
                test_end=DataTester.timestamp_now(),
                html=test.html,
            )
            test_result_list.append(test_result.copy())

        return test_result_list

    def get_daily_run_count(self) -> dict:
        """
        returns a dictonary {"test_id": daily_run_count: int}
        if there has not been any run on that day, an empty dictonary will be returned
        """
        test_id = F.concat_ws("/", "test_group", "test_type", "test_name", "dataset")
        return (
            self.spark.read.format("delta")
            .load(self.config["definitions"]["sql_dataset_path"])
            .withColumn("test_id", test_id)
            .filter(F.col("time") > datetime.date.today())
            .groupBy("test_id")
            .agg(F.count("time").alias("count"))
        ).rdd.collectAsMap()

    def send_digest(self, on_run: tuple[int]) -> None:
        if run_count := self.get_daily_run_count().values():
            # This handles the empty dict case for run count = 0
            if max(run_count) in on_run or 0 in on_run:
                notifier = Notifier(spark=self.spark, config=self.config)
                notifier.send_digest(level=ResultStatus.success if self.testing else ResultStatus.critical)

    def send_notification(self) -> None:
        notifier = Notifier(spark=self.spark, config=self.config)
        notifier.send_notifications()

    def test_runner(self, dataset: Optional[str] = None, show_result: bool = False) -> None:
        loader.load_plugins(self.config["plugins"])

        if dataset:
            print(f"Running tests on {self.config['env']}/{dataset}")
            test_result_list = self.run_data_tests(dataset=dataset)
        else:
            # execute the tests on the readable dataframes
            test_result_list = []
            for dataset in self.config["datasets"]:
                print(f"Running tests on {self.config['env']}/{dataset}")
                test_result_list += self.run_data_tests(dataset=dataset)  # type: ignore
        write_to_monitoring_table(
            test_result_list,
            self.config["definitions"].get("sql_table_name"),
            self.config["definitions"]["sql_dataset_path"],
            self.spark,
        )

        if show_result:
            show_monitoring_table(test_result_list, self.spark)
