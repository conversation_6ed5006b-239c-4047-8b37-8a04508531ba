definitions:
  sql_dataset_path: &sql_dataset_path /Volumes/westeurope_crowd_analytics/faplytics_prod/faplytics/faplytics-monitoring/data/
  sql_table_name: faplytics.monitoring
  tests:
  - test: &last_modified
      type: last_modified
      name: last_modified
      group: basic
      parameters: {}
  - test: &data_count
      type: data_count
      name: data_count
      group: basic
      parameters:
        monitoring_dataset_path: *sql_dataset_path
        time_column: received_date
        sql: ""
        count_var: "data_count"
  - test: &oso_data_count
      type: oso_data_count
      name: oso_data_count
      group: basic
      parameters:
        time_column: incident_time
        monitoring_dataset_path: *sql_dataset_path
  - test: &schema_monitor
      type: schema_monitor
      name: schema_monitor
      group: basic
      parameters:
        monitoring_dataset_path: *sql_dataset_path

  parameters:
    - group: &three_days_thresholds
        success_threshold_mins: 4320
        warning_threshold_mins: 6480
        error_threshold_mins: 8640

plugins:
- last_modified
- data_count
- decoder_errors
- oso_data_count
- schema_monitor
- session_hash_check

datasets:
  fap5_trincs:
    - <<: *last_modified
      parameters:
        time_column: trinc_time
    - <<: *data_count
      parameters:
        time_column: trinc_time
        monitoring_dataset_path: *sql_dataset_path
    - *schema_monitor
  fap5_incidents:
    - <<: *oso_data_count
      run_at: [1]
    - <<: *last_modified
      parameters:
        time_column: incident_time
    - <<: *data_count
      parameters:
        time_column: incident_time
        monitoring_dataset_path: *sql_dataset_path
    - <<: *data_count
      name: vin_count_customer_vehicles
      parameters:
        time_column: incident_time
        monitoring_dataset_path: *sql_dataset_path
        sql: "SELECT * FROM df_table WHERE (LOWER(vin) NOT LIKE '%unknown%' AND NOT test_vehicle)"
        drop_duplicates:
          - vin
        alert_threshold: 1
        count_var: vin_count_without_test_vehicle
    - <<: *data_count
      name: vin_count_test_vehicles
      parameters:
        time_column: incident_time
        monitoring_dataset_path: *sql_dataset_path
        sql: "SELECT * FROM df_table WHERE (LOWER(vin) NOT LIKE '%unknown%' AND test_vehicle)"
        drop_duplicates:
          - vin
        count_var: vin_count_for_test_vehicle
    - <<: *data_count
      name: haf_data_count
      parameters:
        time_column: received_date
        monitoring_dataset_path: *sql_dataset_path
        sql: "SELECT * FROM df_table where contains(sub_system,'HAF');"
        count_var: haf_data_count
    - *schema_monitor
  fap4_ingest_events:
    - *last_modified
    - <<: *data_count
      parameters:
        time_column: backend_process_date
        date_format: "%Y-%m"
        monitoring_dataset_path: *sql_dataset_path
  fap4_events:
    - <<: *last_modified
      parameters:
        time_column: backend_process_date
    - <<: *data_count
      parameters:
        time_column: backend_process_date
        monitoring_dataset_path: *sql_dataset_path
  fap5_incidents_decoded:
    - *last_modified
    - type: decoder_errors
      name: decoder_errors
      group: basic
    - type: session_hash_check
      name: session_hash_check
      group: basic
  fap5_histograms_decoded:
    - *last_modified
    - *data_count
    - type: decoder_errors
      name: decoder_errors
      group: basic
  fap5_histograms:
    - *last_modified
    - *data_count
  fap5_images:
    - <<: *data_count
      parameters:
        time_column: date
        duration_hours: 48
        monitoring_dataset_path: *sql_dataset_path
  fap5_images_predictions:
    - <<: *data_count
      parameters:
        time_column: date
        duration_hours: 48
        monitoring_dataset_path: *sql_dataset_path
  raw_snapshots:
    - *data_count
  ingest_snapshots:
    - *last_modified
    - <<: *data_count
      parameters:
        monitoring_dataset_path: *sql_dataset_path
        time_column: received_date
        date_format: "%Y-%m"
  vvr_emea:
    - <<: *last_modified
      parameters:
        time_column: timestamp
        error_threshold_mins: 360
        dataset_path: abfss://<EMAIL>/delta_events/data
    - <<: *data_count
      parameters:
        time_column: timestamp
        dataset_path: abfss://<EMAIL>/delta_events/data
        monitoring_dataset_path: *sql_dataset_path
  vvr_amap:
    - <<: *last_modified
      parameters:
        time_column: timestamp
        error_threshold_mins: 360
        dataset_path: abfss://<EMAIL>/amap/delta_events/data
    - <<: *data_count
      parameters:
        time_column: timestamp
        dataset_path: abfss://<EMAIL>/amap/delta_events/data
        monitoring_dataset_path: *sql_dataset_path
  rwup_raw_emea:
    - <<: *last_modified
      parameters:
        <<: *three_days_thresholds
        dataset_path: abfss://<EMAIL>/events_processed/data
        time_column: eventtsms
  rwup_raw_amap:
    - <<: *last_modified
      parameters:
        <<: *three_days_thresholds
        dataset_path: abfss://<EMAIL>/amap/events_processed/data
        time_column: eventtsms
  c2x_emea:
    - <<: *last_modified
      parameters:
        time_column: eventts
        error_threshold_mins: 360
        dataset_path: abfss://<EMAIL>/events/data
    - <<: *data_count
      parameters:
        time_column: eventts
        dataset_path: abfss://<EMAIL>/events/data
        monitoring_dataset_path: *sql_dataset_path
