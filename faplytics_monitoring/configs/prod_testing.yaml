definitions:
  sql_dataset_path: &sql_dataset_path /tmp/faplytics_monitoring/data
  tests:
  - test: &last_modified
      type: last_modified
      name: last_modified
      group: basic
      parameters: {}
  - test: &schema_monitor
      type: schema_monitor
      name: schema_monitor
      group: basic
      parameters:
        monitoring_dataset_path: *sql_dataset_path

  parameters:
    - group: &three_days_thresholds
        success_threshold_mins: 4320
        warning_threshold_mins: 6480
        error_threshold_mins: 8640

plugins:
- last_modified
- schema_monitor
- session_hash_check

datasets:
  fap5_trincs:
    - <<: *schema_monitor
  vvr_emea:
    - <<: *last_modified
      parameters:
        dataset_path: /mnt/stage_data/prod/vvr/events/data
        time_column: eventtsms
  rwup_raw_emea:
    - <<: *last_modified
      parameters:
        <<: *three_days_thresholds
        dataset_path: /mnt/stage_data/prod/vvr/events_processed/data
        time_column: eventtsms
  fap5_incidents_decoded:
    - type: session_hash_check
      name: session_hash_check
      group: basic
