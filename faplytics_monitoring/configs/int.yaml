definitions:
  sql_dataset_path: &sql_dataset_path /Volumes/westeurope_crowd_analytics/faplytics_int/faplytics/faplytics-monitoring/data/
  sql_table_name: faplytics_int.monitoring
  tests:
  - test: &last_modified
      type: last_modified
      name: last_modified
      group: basic
      parameters:
        success_threshold_mins: 240
        warning_threshold_mins: 360
        error_threshold_mins: 480
  - test: &data_count
      type: data_count
      name: data_count
      group: basic
      parameters:
        monitoring_dataset_path: *sql_dataset_path
        iqr_multiplier: 4
        time_column: received_date
  - test: &oso_data_count
      type: oso_data_count
      name: oso_data_count
      group: basic
      parameters:
        time_column: incident_time
        monitoring_dataset_path: *sql_dataset_path
  - test: &schema_monitor
      type: schema_monitor
      name: schema_monitor
      group: basic
      parameters:
        monitoring_dataset_path: *sql_dataset_path
  parameters:
    - group: &one_day_thresholds
        success_threshold_mins: 720
        warning_threshold_mins: 1080
        error_threshold_mins: 1440
    - group: &two_day_thresholds
        success_threshold_mins: 1440
        warning_threshold_mins: 2160
        error_threshold_mins: 2880


plugins:
- last_modified
- data_count
- incident_burst_int
- decoder_errors
- oso_data_count
- schema_monitor
- session_hash_check

datasets:
  fap5_trincs:
    - <<: *last_modified
      parameters:
        time_column: trinc_time
        <<: *one_day_thresholds
    - <<: *data_count
      parameters:
        time_column: trinc_time
        monitoring_dataset_path: *sql_dataset_path
    - *schema_monitor
  fap5_incidents:
    - <<: *oso_data_count
      run_at: [1]
    - <<: *last_modified
      parameters:
        time_column: incident_time
        <<: *one_day_thresholds
    - <<: *data_count
      parameters:
        time_column: incident_time
        monitoring_dataset_path: *sql_dataset_path
    - <<: *data_count
      name: haf_data_count
      parameters:
        time_column: received_date
        monitoring_dataset_path: *sql_dataset_path
        sql: "SELECT * FROM df_table where contains(sub_system,'HAF');"
        count_var: haf_data_count
    - type: incident_burst_int
      name: incident_burst_int
      group: basic
    - *schema_monitor
  fap4_ingest_events:
    - <<: *last_modified
      parameters:
        time_column: backend_process_date
        <<: *two_day_thresholds
  fap4_events:
    - <<: *last_modified
      parameters:
        time_column: backend_process_date
        <<: *two_day_thresholds
  fap5_incidents_decoded:
    - <<: *last_modified
      parameters:
        <<: *one_day_thresholds
    - type: decoder_errors
      name: decoder_errors
      group: basic
    - type: session_hash_check
      name: session_hash_check
      group: basic
  fap5_histograms_decoded:
    - <<: *last_modified
      parameters:
        <<: *one_day_thresholds
    - *data_count
    - type: decoder_errors
      name: decoder_errors
      group: basic
  fap5_histograms:
    - <<: *last_modified
      parameters:
        <<: *one_day_thresholds
    - *data_count
  fap5_images:
    - <<: *data_count
      parameters:
        time_column: date
        duration_hours: 48
        monitoring_dataset_path: *sql_dataset_path
  fap5_images_predictions:
    - <<: *data_count
      parameters:
        time_column: date
        duration_hours: 48
        monitoring_dataset_path: *sql_dataset_path
  fap5_images_ar:
    - <<: *data_count
      parameters:
        time_column: date
        duration_hours: 48
        monitoring_dataset_path: *sql_dataset_path
  raw_snapshots:
    - *data_count
  ingest_snapshots:
    - <<: *data_count
      parameters:
        monitoring_dataset_path: *sql_dataset_path
        time_column: received_date
        date_format: "%Y-%m"
    - <<: *last_modified
      parameters:
        <<: *two_day_thresholds
  vvr_emea:
    - <<: *last_modified
      parameters:
        time_column: timestamp
        error_threshold_mins: 360
        dataset_path: abfss://<EMAIL>/delta_events/data
    - <<: *data_count
      parameters:
        time_column: timestamp
        dataset_path: abfss://<EMAIL>/delta_events/data
        monitoring_dataset_path: *sql_dataset_path
  c2x_emea:
    - <<: *last_modified
      parameters:
        time_column: eventts
        error_threshold_mins: 360
        dataset_path: abfss://<EMAIL>/events/data
    - <<: *data_count
      parameters:
        time_column: eventts
        dataset_path: abfss://<EMAIL>/events/data
        monitoring_dataset_path: *sql_dataset_path
