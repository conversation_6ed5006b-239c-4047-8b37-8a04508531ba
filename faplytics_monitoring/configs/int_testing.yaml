definitions:
  sql_dataset_path: &sql_dataset_path /tmp/faplytics_monitoring/data
  tests:
  - test: &schema_monitor
      type: schema_monitor
      name: schema_monitor
      group: basic
      parameters:
        monitoring_dataset_path: *sql_dataset_path

plugins:
- incident_burst_int
- decoder_errors
- schema_monitor
- session_hash_check

datasets:
  fap5_trincs:
    - <<: *schema_monitor
  fap5_incidents:
    - type: incident_burst_int
      name: incident_burst_int
      group: basic
  fap5_incidents_decoded:
    - type: decoder_errors
      name: decoder_errors
      group: basic
    - type: session_hash_check
      name: session_hash_check
      group: basic
  fap5_histograms_decoded:
    - type: decoder_errors
      name: decoder_errors
      group: basic
