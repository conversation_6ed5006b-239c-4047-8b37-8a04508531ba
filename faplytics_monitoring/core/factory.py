"""Factory for creating a faplytics data test."""

from typing import Any, Callable, Dict

from faplytics_monitoring.core.datatest import FaplyticsDataTest

test_creation_funcs: Dict[str, Callable[..., FaplyticsDataTest]] = {}


def register(test_type: str, creator_fn: Callable[..., FaplyticsDataTest]) -> None:
    """Register a new faplytics data test type."""
    test_creation_funcs[test_type] = creator_fn


def create(arguments: Dict[str, Any], dataset: Any) -> FaplyticsDataTest:
    """Create a faplytics data test of a specific type, given JSON data."""
    args_copy = arguments.copy()
    test_type = args_copy["type"]
    parameters = args_copy.get("parameters", {})
    try:
        creator_func = test_creation_funcs[test_type]
    except KeyError:
        raise ValueError(f"unknown test type {test_type!r}") from None
    return creator_func(dataset, **parameters)
