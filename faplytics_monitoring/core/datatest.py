"""Represents a basic faplytics data test."""
from abc import ABC, abstractmethod
from typing import Optional

from pyspark.sql import SparkSession

from faplytics_monitoring.core.errors import ResultStatus


class FaplyticsDataTest(ABC):
    """Basic representation of a faplytics data test."""

    @abstractmethod
    def run(self, spark: SparkSession) -> None:
        """Starts the test."""
        ...

    @abstractmethod
    def __post_init__(self) -> None:
        """should be implemented to set defaults"""
        self.status_code: ResultStatus
        self.message: Optional[dict[str, str]]
        self.html: Optional[str]
