"""A simple plugin loader."""
import importlib


class ModuleInterface:
    """Represents a plugin interface. A plugin has a single register function."""

    @staticmethod
    def register() -> None:
        """Register the necessary items in the faplytics data test factory."""


def import_module(name: str) -> ModuleInterface:
    """Imports a module given a name."""
    return importlib.import_module(name)  # type: ignore


def load_plugins(plugins: list) -> None:
    """Discovers and registers plugins"""

    for plugin_file in plugins:
        plugin = import_module(f"faplytics_monitoring.plugins.{plugin_file}")
        plugin.register()
