import os
from datetime import datetime, timed<PERSON><PERSON>
from pathlib import Path
from typing import Optional, Union

import pydantic
from delta.tables import DeltaTable
from pydantic_spark.base import SparkBase
from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import col
from pyspark.sql.types import StructType


class ResultModel(SparkBase):
    time: datetime = datetime.utcnow()
    dataset: str
    test_type: str
    test_name: Optional[str] = None
    test_group: Optional[str] = None
    test_result_code: int
    test_result_map: Optional[dict[str, str]] = None
    test_start: datetime = pydantic.Field(default_factory=lambda: datetime(1970, 1, 1))
    test_end: datetime = pydantic.Field(default_factory=lambda: datetime(1970, 1, 1))
    html: Optional[str] = None


def convert_to_spark_dataset(data: list[ResultModel], schema: StructType, spark: SparkSession) -> DataFrame:

    df = spark.createDataFrame(data=data, schema=schema)  # type: ignore
    return df


def create_or_vacuum_table(
    table_name: str,
    table_location: Union[str, Path],
    schema: StructType,
    spark: SparkSession,
    keep_ndays: int = 30,
    skip_vacuum: bool = True,
) -> DeltaTable:
    """
    Create deltalake backed sql table
    returns the table name of the created table {database}.{name}
    """
    table_builder = DeltaTable.createIfNotExists(spark)
    if table_name:
        table_builder = table_builder.tableName(table_name)
    table = table_builder.addColumns(schema).location(str(table_location)).execute()

    if not skip_vacuum:
        table.delete(col("time") < (datetime.utcnow() - timedelta(days=keep_ndays)))
        table.vacuum(168)
    return table


def write_to_monitoring_table(
    test_result_list: list[ResultModel], sql_table_name: str, sql_table_location: Union[str, Path], spark: SparkSession
):
    schema = StructType.fromJson(ResultModel.spark_schema())
    skip_vacuum = os.getenv("SKIP_VACUUM", "1").lower() in ("1", "true", "yes", "y")
    _ = create_or_vacuum_table(sql_table_name, sql_table_location, schema, spark, skip_vacuum=skip_vacuum)
    df = convert_to_spark_dataset(test_result_list, schema, spark)
    df.write.mode("append").format("delta").option("mergeSchema", "true").save(str(sql_table_location))


def show_monitoring_table(test_result_list: list[ResultModel], spark: SparkSession) -> DataFrame:
    schema = StructType.fromJson(ResultModel.spark_schema())
    df = convert_to_spark_dataset(test_result_list, schema, spark)
    df.show(truncate=False)

    return df
