import dataclasses
import itertools
import os
import pathlib
from datetime import datetime, timedelta

import delta
import pyspark.sql
import pyspark.sql.functions as F
import pytz
import requests
from pydantic_spark.base import SparkBase
from pyspark.sql import Window
from pyspark.sql.types import StructType

from faplytics_monitoring.core.dataset_utils import ResultModel, create_or_vacuum_table
from faplytics_monitoring.core.errors import ResultStatus


class NotificationMessage:
    def __init__(self, message: str):
        self.message = message
        self._webhook_url = os.environ["WEBHOOK_URL"]

    def send(self):

        payload = {
            "type": "message",
            "attachments": [
                {
                    "contentType": "application/vnd.microsoft.teams.card.o365connector",
                    "content": {
                        "@type": "MessageCard",
                        "@context": "https://schema.org/extensions",
                        "summary": "Summary",
                        "version": "1.0",
                        "sections": [
                            {"text": self.message},
                        ],
                    },
                }
            ],
        }

        if self._webhook_url == "print":
            print("Sending notification:")
            print(self.message)
        else:
            r = requests.post(self._webhook_url, json=payload)
            r.raise_for_status()


class NotificationState:

    RECOVERED = "recovered"
    FAILED = "failed"


class NotificationRecord(SparkBase):

    time: datetime
    test_time: datetime
    test_id: str
    state: str


@dataclasses.dataclass
class NotificationResult:

    failed: list[ResultModel]
    recovered: list[ResultModel]


def _get_state_table(config: dict) -> delta.DeltaTable:

    sql_ds_path = pathlib.Path(config["definitions"]["sql_dataset_path"])
    notification_table = sql_ds_path.with_name("notifications")
    skip_vacuum = os.getenv("SKIP_VACUUM", "0").lower() in ("1", "true", "yes", "y")
    delta_table = create_or_vacuum_table(
        spark=pyspark.sql.SparkSession.getActiveSession(),
        table_name="",
        table_location=os.fspath(notification_table),
        schema=StructType.fromJson(NotificationRecord.spark_schema()),
        skip_vacuum=skip_vacuum,
    )
    return delta_table


class Notifier:

    config: dict
    spark: pyspark.sql.SparkSession
    env: str

    def __init__(self, config: dict, spark: pyspark.sql.SparkSession):
        self.config = config
        self.spark = spark
        self.env = config["env"]

    def _load_results(self) -> pyspark.sql.DataFrame:
        test_id = F.concat_ws("/", "test_group", "test_type", "test_name", "dataset")
        return (
            self.spark.read.format("delta")
            .load(self.config["definitions"]["sql_dataset_path"])
            .withColumn("test_id", test_id)
        )

    def send_digest(self, level: ResultStatus = ResultStatus.critical) -> NotificationResult:

        window = Window.partitionBy("test_id").orderBy(F.col("time").desc())
        level = level.value

        results = self._load_results()
        state = self.read_notified_state()
        digest_key = ("__digest__", "")
        last_digest = state.get(digest_key)

        # get all results since the last digest or the last 24 hours
        if last_digest:
            since = last_digest.time
        else:
            since = datetime.utcnow() - timedelta(days=1)

        failed_tests = (
            results.filter(results.time >= since)
            .withColumn(
                "row",
                F.row_number().over(window),
            )
            .filter(F.col("row") == 1)
            .filter(F.col("test_result_code") >= level)  # type: ignore
            .drop("test_id", "row")
            .collect()
        )

        messages = []
        for row in failed_tests:
            messages.append(ResultModel(**row.asDict()))

        if messages:
            since_text = since.astimezone().astimezone(pytz.timezone("Europe/Berlin")).strftime("%d %b, %H:%M %Z")
            make_notification_message(
                title=f"Faplytics notification digest on {self.env} since {since_text}",
                failed_tests=messages,
                recovered_tests=[],
            )

        now = datetime.utcnow()
        self.write_notified_state(
            [NotificationRecord(time=now, test_time=now, test_id=digest_key[0], state=digest_key[1])]
        )
        return NotificationResult(failed=messages, recovered=[])

    def send_notifications(self, level: ResultStatus = ResultStatus.error) -> NotificationResult:

        window = Window.partitionBy("test_id").orderBy(F.col("time").desc())
        level = level.value

        # per test, we get the latest execution and the result code from the before-latest execution
        notify_candidates = (
            self._load_results()
            .withColumn(
                "row",
                F.row_number().over(window),
            )
            .withColumn(
                "previous_result_code",
                F.lead("test_result_code", offset=1, default=-1).over(window),
            )
            .filter(F.col("row") == 1)
            .filter(F.col("previous_result_code") != F.col("test_result_code"))
            .collect()
        )

        notified_state = self.read_notified_state()

        result_fields = StructType.fromJson(ResultModel.spark_schema()).names

        # calculate the silence timestamp
        # as long as there were any notifications after this, we don't send new ones
        now = datetime.now()
        silence_marker = now - timedelta(hours=self.config["definitions"].get("notification_silence_hours", 24))

        candidates = {NotificationState.RECOVERED: [], NotificationState.FAILED: []}
        to_notify = []
        for candidate in notify_candidates:
            test_id = candidate["test_id"]
            result_code = candidate["test_result_code"]
            prev_result_code = candidate["previous_result_code"]

            if result_code >= level > prev_result_code:
                state = NotificationState.FAILED
            elif result_code < level <= prev_result_code:
                state = NotificationState.RECOVERED
            else:
                state = None

            last_notify = notified_state.get((test_id, state))
            if last_notify and (last_notify.time > silence_marker or last_notify.test_time == candidate["time"]):
                continue

            if state:
                candidates[state].append(ResultModel(**{f: candidate[f] for f in result_fields}))
                to_notify.append(
                    NotificationRecord(time=now, state=state, test_id=test_id, test_time=candidate["time"])
                )

        if to_notify:
            make_notification_message(
                title=f"Faplytics monitoring updates from {self.env}",
                failed_tests=candidates[NotificationState.FAILED],
                recovered_tests=candidates[NotificationState.RECOVERED],
            )
            self.write_notified_state(to_notify)

        return NotificationResult(**candidates)

    def read_notified_state(self) -> dict[(str, str), NotificationRecord]:

        window = Window.partitionBy("test_id", "state").orderBy(F.col("time").desc())
        latest = (
            _get_state_table(self.config)
            .toDF()
            .withColumn(
                "row",
                F.row_number().over(window),
            )
            .filter(F.col("row") == 1)
            .drop("row")
        )

        return {(row["test_id"], row["state"]): NotificationRecord(**row.asDict()) for row in latest.collect()}

    def write_notified_state(self, notifications: list[NotificationRecord]):

        spark = pyspark.sql.SparkSession.getActiveSession()
        notify_schema = StructType.fromJson(NotificationRecord.spark_schema())
        inserts = spark.createDataFrame(notifications, schema=notify_schema)

        values = {col: col for col in notify_schema.names}
        _get_state_table(self.config).merge(inserts, condition="false").whenNotMatchedInsert(values=values).execute()


def _make_message_section(results: list[ResultModel], failed: bool) -> list[str]:

    results.sort(key=lambda r: (r.dataset, r.test_type))
    message = []
    for dataset, ds_results in itertools.groupby(results, key=lambda r: r.dataset):
        if failed:
            headline = f"🔴 Critical errors occurred for {dataset}!"
        else:
            headline = f"🟢 Errors recovered for {dataset} 🎉"

        message.append(headline)
        for result in ds_results:
            message.append(f"  * {result.test_type}: `{str(result.test_result_map)}`")

        # teams needs an extra empty line after the bullet points for proper formatting
        message.append("")

    return message


def make_notification_message(failed_tests: list[ResultModel], recovered_tests: list[ResultModel], title=""):

    parts = [f"# **{title}**"]

    if recovered_tests:
        parts += _make_message_section(recovered_tests, failed=False)

    if failed_tests:
        parts += _make_message_section(failed_tests, failed=True)

    message = "\n".join(parts)
    email = NotificationMessage(message=message)
    email.send()
