[build-system]
requires = ["setuptools", "setuptools-scm"]
build-backend = "setuptools.build_meta"

[project]
name = "faplytics-monitoring"
description = "faplytics monitoring with plugin architecture"
readme = "README.md"
requires-python = ">=3.9"
classifiers = [
    "Programming Language :: Python :: 3",
]
dependencies = [
    "pytz>=2020.5",
    "faplytics==3.4.1",
    "faplytics-fap5",
    "pydantic>=1.10.2",
    "pydantic-spark>=0.2.0,<=0.3.0",
    "pyspark~=3.5.0",
    "requests~=2.28",
    "dateparser~=1.1.8",
    "delta-spark",
    "click"
]
dynamic = ["version"]

[project.optional-dependencies]
dev = ["pytest", "pytest-cov", "pre-commit", "pytest-mock", "setuptools_scm"]

[project.scripts]
faplytics-monitoring = "faplytics_monitoring.cli:cli_runner"


[tool.pytest.ini_options]
addopts = "--junitxml=test-results.xml --cov=faplytics_monitoring --cov-report xml"

[tool.setuptools]
packages = ["faplytics_monitoring"]

[tool.setuptools_scm]

[tool.black]
line-length = 120

[tool.isort]
profile = "black"


[tool.flake8]
ignore = ["E501", "W503"]
max-line-length = 120
per-file-ignores = [
    "*/__init__.py:F401"
]
select = ["F", "W", "C90", "B"]
